import React, { useEffect, useRef, useCallback } from 'react';

interface Node {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  opacity: number;
  hue: number;
  pulsePhase: number;
}

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  opacity: number;
  hue: number;
  life: number;
  maxLife: number;
}

interface MolecularBackgroundProps {
  nodeCount?: number;
  particleCount?: number;
  maxConnections?: number;
  animationSpeed?: number;
  className?: string;
}

export const MolecularBackground: React.FC<MolecularBackgroundProps> = ({
  nodeCount = 35,
  particleCount = 20,
  maxConnections = 4,
  animationSpeed = 0.3,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const nodesRef = useRef<Node[]>([]);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>();
  const timeRef = useRef<number>(0);

  // 初始化画布和节点
  const initCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // 设置高DPI支持
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';
    
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.scale(dpr, dpr);
    }

    // 初始化主要节点
    const nodes: Node[] = [];
    for (let i = 0; i < nodeCount; i++) {
      nodes.push({
        x: Math.random() * rect.width,
        y: Math.random() * rect.height,
        vx: (Math.random() - 0.5) * animationSpeed,
        vy: (Math.random() - 0.5) * animationSpeed,
        radius: Math.random() * 5 + 3,
        opacity: Math.random() * 0.6 + 0.2,
        hue: Math.random() * 60 + 200, // 蓝色到紫色范围
        pulsePhase: Math.random() * Math.PI * 2
      });
    }
    nodesRef.current = nodes;

    // 初始化漂浮粒子
    const particles: Particle[] = [];
    for (let i = 0; i < particleCount; i++) {
      const life = Math.random() * 200 + 100;
      particles.push({
        x: Math.random() * rect.width,
        y: Math.random() * rect.height,
        vx: (Math.random() - 0.5) * animationSpeed * 0.5,
        vy: (Math.random() - 0.5) * animationSpeed * 0.5,
        radius: Math.random() * 3 + 1,
        opacity: Math.random() * 0.4 + 0.1,
        hue: Math.random() * 40 + 180, // 青色到蓝色范围
        life: life,
        maxLife: life
      });
    }
    particlesRef.current = particles;
  }, [nodeCount, particleCount, animationSpeed]);

  // 创建渐变
  const createGradient = (ctx: CanvasRenderingContext2D, x1: number, y1: number, x2: number, y2: number, hue1: number, hue2: number, alpha: number = 0.6) => {
    const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
    gradient.addColorStop(0, `hsla(${hue1}, 70%, 60%, ${alpha})`);
    gradient.addColorStop(1, `hsla(${hue2}, 70%, 60%, ${alpha})`);
    return gradient;
  };

  // 动画循环
  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    timeRef.current += 0.016; // 约60fps

    // 清除画布，使用深色渐变背景
    const bgGradient = ctx.createRadialGradient(
      rect.width / 2, rect.height / 2, 0,
      rect.width / 2, rect.height / 2, Math.max(rect.width, rect.height) / 2
    );
    bgGradient.addColorStop(0, '#0f172a');
    bgGradient.addColorStop(0.5, '#1e293b');
    bgGradient.addColorStop(1, '#0f172a');
    
    ctx.fillStyle = bgGradient;
    ctx.fillRect(0, 0, rect.width, rect.height);

    // 添加微妙的光晕效果
    const glowGradient = ctx.createRadialGradient(
      rect.width / 2, rect.height / 2, 0,
      rect.width / 2, rect.height / 2, Math.max(rect.width, rect.height) / 1.5
    );
    glowGradient.addColorStop(0, 'rgba(37, 99, 235, 0.03)');
    glowGradient.addColorStop(0.7, 'rgba(8, 145, 178, 0.02)');
    glowGradient.addColorStop(1, 'transparent');
    
    ctx.fillStyle = glowGradient;
    ctx.fillRect(0, 0, rect.width, rect.height);

    const nodes = nodesRef.current;
    const particles = particlesRef.current;

    // 更新主要节点
    nodes.forEach(node => {
      node.x += node.vx;
      node.y += node.vy;
      node.pulsePhase += 0.02;

      // 边界反弹
      if (node.x <= 0 || node.x >= rect.width) {
        node.vx *= -1;
        node.x = Math.max(0, Math.min(rect.width, node.x));
      }
      if (node.y <= 0 || node.y >= rect.height) {
        node.vy *= -1;
        node.y = Math.max(0, Math.min(rect.height, node.y));
      }

      // 脉动效果
      node.opacity = 0.3 + Math.sin(node.pulsePhase) * 0.3;
    });

    // 更新漂浮粒子
    particles.forEach((particle, index) => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life--;

      // 粒子生命周期
      if (particle.life <= 0) {
        const life = Math.random() * 200 + 100;
        particle.life = life;
        particle.maxLife = life;
        particle.x = Math.random() * rect.width;
        particle.y = Math.random() * rect.height;
        particle.vx = (Math.random() - 0.5) * animationSpeed * 0.5;
        particle.vy = (Math.random() - 0.5) * animationSpeed * 0.5;
        particle.hue = Math.random() * 40 + 180;
      }

      // 边界处理
      if (particle.x <= 0 || particle.x >= rect.width) particle.vx *= -1;
      if (particle.y <= 0 || particle.y >= rect.height) particle.vy *= -1;
      particle.x = Math.max(0, Math.min(rect.width, particle.x));
      particle.y = Math.max(0, Math.min(rect.height, particle.y));

      // 根据生命周期调整透明度
      particle.opacity = (particle.life / particle.maxLife) * 0.4 + 0.1;
    });

    // 绘制连接线（增强版）
    ctx.lineCap = 'round';
    for (let i = 0; i < nodes.length; i++) {
      let connections = 0;
      for (let j = i + 1; j < nodes.length && connections < maxConnections; j++) {
        const dx = nodes[i].x - nodes[j].x;
        const dy = nodes[i].y - nodes[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const maxDistance = 150;

        if (distance < maxDistance) {
          const opacity = (1 - distance / maxDistance) * 0.2;
          const avgHue = (nodes[i].hue + nodes[j].hue) / 2;
          
          // 渐变连接线
          ctx.strokeStyle = `hsla(${avgHue}, 60%, 60%, ${opacity})`;
          ctx.lineWidth = Math.max(0.5, 2 - distance / 100);
          
          // 添加发光效果
          ctx.shadowColor = `hsla(${avgHue}, 60%, 60%, ${opacity * 0.3})`;
          ctx.shadowBlur = 2;
          
          ctx.beginPath();
          ctx.moveTo(nodes[i].x, nodes[i].y);
          ctx.lineTo(nodes[j].x, nodes[j].y);
          ctx.stroke();
          
          ctx.shadowBlur = 0;
          connections++;
        }
      }
    }

    // 绘制粒子连接线
    for (let i = 0; i < particles.length; i++) {
      for (let j = 0; j < nodes.length; j++) {
        const dx = particles[i].x - nodes[j].x;
        const dy = particles[i].y - nodes[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const maxDistance = 80;

        if (distance < maxDistance) {
          const opacity = (1 - distance / maxDistance) * 0.1;
          const avgHue = (particles[i].hue + nodes[j].hue) / 2;
          
          ctx.strokeStyle = `hsla(${avgHue}, 50%, 50%, ${opacity})`;
          ctx.lineWidth = 0.5;
          ctx.setLineDash([2, 4]);
          
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(nodes[j].x, nodes[j].y);
          ctx.stroke();
          
          ctx.setLineDash([]);
        }
      }
    }

    // 绘制主要节点（增强版）
    nodes.forEach(node => {
      const pulseRadius = node.radius + Math.sin(node.pulsePhase) * 0.5;
      
      // 外层光环
      ctx.beginPath();
      ctx.arc(node.x, node.y, pulseRadius + 3, 0, Math.PI * 2);
      const outerGradient = ctx.createRadialGradient(
        node.x, node.y, 0,
        node.x, node.y, pulseRadius + 3
      );
      outerGradient.addColorStop(0, `hsla(${node.hue}, 70%, 60%, ${node.opacity * 0.3})`);
      outerGradient.addColorStop(1, 'transparent');
      ctx.fillStyle = outerGradient;
      ctx.fill();

      // 主节点
      ctx.beginPath();
      ctx.arc(node.x, node.y, pulseRadius, 0, Math.PI * 2);
      const nodeGradient = ctx.createRadialGradient(
        node.x, node.y, 0,
        node.x, node.y, pulseRadius
      );
      nodeGradient.addColorStop(0, `hsla(${node.hue}, 80%, 70%, ${node.opacity})`);
      nodeGradient.addColorStop(1, `hsla(${node.hue}, 60%, 50%, ${node.opacity * 0.3})`);
      ctx.fillStyle = nodeGradient;
      ctx.fill();

      // 内核高光
      ctx.beginPath();
      ctx.arc(node.x - pulseRadius * 0.3, node.y - pulseRadius * 0.3, pulseRadius * 0.3, 0, Math.PI * 2);
      ctx.fillStyle = `hsla(${node.hue}, 90%, 80%, ${node.opacity * 0.6})`;
      ctx.fill();
    });

    // 绘制漂浮粒子
    particles.forEach(particle => {
      const alpha = Math.sin(timeRef.current * 2 + particle.x * 0.01) * 0.3 + 0.7;
      
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      const particleGradient = ctx.createRadialGradient(
        particle.x, particle.y, 0,
        particle.x, particle.y, particle.radius
      );
      particleGradient.addColorStop(0, `hsla(${particle.hue}, 60%, 60%, ${particle.opacity * alpha})`);
      particleGradient.addColorStop(1, 'transparent');
      ctx.fillStyle = particleGradient;
      ctx.fill();
    });

    // 添加动态光效
    const lightX = rect.width / 2 + Math.sin(timeRef.current * 0.5) * rect.width * 0.3;
    const lightY = rect.height / 2 + Math.cos(timeRef.current * 0.3) * rect.height * 0.2;
    
    const lightGradient = ctx.createRadialGradient(
      lightX, lightY, 0,
      lightX, lightY, 200
    );
    lightGradient.addColorStop(0, 'rgba(37, 99, 235, 0.05)');
    lightGradient.addColorStop(0.5, 'rgba(8, 145, 178, 0.03)');
    lightGradient.addColorStop(1, 'transparent');
    
    ctx.fillStyle = lightGradient;
    ctx.fillRect(0, 0, rect.width, rect.height);

    animationRef.current = requestAnimationFrame(animate);
  }, [maxConnections, animationSpeed]);

  const handleResize = useCallback(() => {
    initCanvas();
  }, [initCanvas]);

  useEffect(() => {
    initCanvas();
    
    window.addEventListener('resize', handleResize);
    animate();

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [initCanvas, animate, handleResize]);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none ${className}`}
      style={{ 
        width: '100vw', 
        height: '100vh',
        zIndex: -1,
        position: 'fixed',
        top: 0,
        left: 0
      }}
    />
  );
};