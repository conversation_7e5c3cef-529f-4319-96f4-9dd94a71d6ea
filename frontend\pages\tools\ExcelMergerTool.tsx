import React, { useState } from 'react';
import { Layout, Typography, Button, Statistic, message } from 'antd';
import { FileSpreadsheet, Upload, Merge, Download } from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

export const ExcelMergerTool: React.FC = () => {
    const [loading, setLoading] = useState(false);

    const handleMerge = () => {
        setLoading(true);
        message.info('Excel合并功能正在开发中...');
        setTimeout(() => setLoading(false), 2000);
    };

    return (
        <div style={{ width: '100%', height: 'calc(100vh - 160px)', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%' }}>
                <Sider width={280} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                            <Statistic 
                                title={<Text className="!text-gray-300">待合并文件</Text>} 
                                value={0} 
                                prefix={<FileSpreadsheet size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                            <Statistic 
                                title={<Text className="!text-gray-300">合并结果</Text>} 
                                value={0} 
                                prefix={<Merge size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                        </div>
                        
                        <Title level={5} className="!text-white" style={{ marginTop: '24px' }}>快速操作</Title>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Button 
                                type="primary" 
                                icon={<Upload size={16} />} 
                                onClick={handleMerge}
                                loading={loading}
                                style={{ width: '100%' }}
                            >
                                开始合并
                            </Button>
                            <Button 
                                icon={<Download size={16} />} 
                                style={{ width: '100%' }} 
                                disabled
                            >
                                下载结果
                            </Button>
                        </div>
                    </GlassmorphicCard>
                </Sider>
                
                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <div style={{ 
                            padding: '48px', 
                            height: '100%', 
                            display: 'flex', 
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                        }}>
                            <FileSpreadsheet size={64} style={{ color: '#22d3ee', marginBottom: '24px' }} />
                            <Title level={3} className="!text-white" style={{ marginBottom: '16px' }}>
                                Excel暴力合并工具
                            </Title>
                            <Paragraph style={{ 
                                color: 'rgba(255, 255, 255, 0.7)', 
                                fontSize: '16px',
                                maxWidth: '400px',
                                lineHeight: 1.6
                            }}>
                                智能合并多个Excel文件和工作表，提高工作效率。
                                该功能正在开发中，敬请期待。
                            </Paragraph>
                        </div>
                    </GlassmorphicCard>
                </Content>
            </Layout>
        </div>
    );
};