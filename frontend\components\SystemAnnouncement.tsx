import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Tag } from 'antd';
import { Megaphone, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';

const { Title, Text } = Typography;

const announcements = [
    {
        id: 1,
        type: '新功能',
        color: 'green',
        title: '股权穿透分析工具震撼上线！',
        content: '深度挖掘公司股权结构，一键生成关系网络图，助力您的商业决策。',
        date: '2024-07-28',
        link: '/tools/equity-penetration',
    },
    {
        id: 2,
        type: '系统维护',
        color: 'orange',
        title: '平台将于今晚进行例行维护',
        content: '为提升服务质量，我们将在凌晨2点至4点进行系统升级，期间服务可能会有短暂中断。',
        date: '2024-07-27',
    },
    {
        id: 3,
        type: '在线讲座',
        color: 'blue',
        title: '专家在线：如何利用数据进行情感分析',
        content: '本周五下午3点，行业专家将在线分享情感分析的最佳实践，欢迎报名参加。',
        date: '2024-07-26',
        link: '/events/1',
    },
];

export const SystemAnnouncement: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? announcements.length - 1 : prevIndex - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === announcements.length - 1 ? 0 : prevIndex + 1));
  };

  useEffect(() => {
    const timer = setTimeout(handleNext, 7000); // Auto-play every 7 seconds
    return () => clearTimeout(timer);
  }, [currentIndex]);

  const latestAnnouncement = announcements[currentIndex];

  return (
    <Card
      style={{
        background: 'linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-surface) 100%)',
        backdropFilter: 'blur(20px) saturate(1.2)',
        border: '1px solid var(--border-primary)',
        borderRadius: '12px',
        boxShadow: '0 4px 24px rgba(0, 0, 0, 0.15)',
        position: 'relative',
        overflow: 'hidden',
        height: '100%'
      }}
      bodyStyle={{ padding: 0, height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {/* 顶部渐变装饰条 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '4px',
        background: `linear-gradient(90deg, var(--accent-primary), var(--accent-secondary))`
      }} />

      <div style={{ padding: '24px', flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '8px',
                    background: 'linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#fff',
                    flexShrink: 0,
                }}>
                    <Megaphone size={20} />
                </div>
                <div>
                    <Tag color={latestAnnouncement.color}>{latestAnnouncement.type}</Tag>
                </div>
            </div>
            <Title level={5} style={{ margin: '0 0 8px 0', color: 'var(--text-primary)', fontWeight: 600, lineHeight: 1.4 }}>
                {latestAnnouncement.title}
            </Title>
            <Text style={{ color: 'var(--text-secondary)', fontSize: '14px', display: 'block' }}>
                {latestAnnouncement.content}
            </Text>
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px' }}>
            <div className="announcement-nav">
                <Button type="text" shape="circle" icon={<ChevronLeft size={16} />} onClick={handlePrev} />
                <Button type="text" shape="circle" icon={<ChevronRight size={16} />} onClick={handleNext} />
            </div>
            {latestAnnouncement.link && (
                <Button type="primary" size="small" href={latestAnnouncement.link}>
                    查看详情
                </Button>
            )}
        </div>
      </div>
    </Card>
  );
}; 