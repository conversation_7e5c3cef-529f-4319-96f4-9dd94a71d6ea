from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8002
    DEBUG: bool = True
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000", "http://30.c13.plus", "http://**************"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "30.c13.plus", "**************"]
    
    # AI模型配置
    OPENAI_API_KEY: str = ""
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    
    # 本地模型配置
    CHAT_MODEL_PATH: str = "models/chatbot"
    SENTIMENT_MODEL_PATH: str = "models/sentiment"
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://user:password@localhost:5432/idealab_nlp"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"


settings = Settings()