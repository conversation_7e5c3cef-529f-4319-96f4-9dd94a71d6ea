import React from 'react';
import { Layout, Button, Typography } from 'antd';
import { ArrowLeft, Home, Database } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

interface ToolLayoutProps {
  title: string;
  children: React.ReactNode;
  subtitle?: string;
  category?: string;
}

export const ToolLayout: React.FC<ToolLayoutProps> = ({ 
  title, 
  children, 
  subtitle,
  category = "工具"
}) => {
  const navigate = useNavigate();

  return (
    <Layout className="min-h-screen">
      <div className="professional-background"></div>
      
      {/* 专业化导航栏 */}
      <Header className="glass-panel border-b border-primary-300 px-6">
        <div className="flex items-center justify-between h-full">
          {/* 左侧导航 */}
          <div className="flex items-center space-x-6">
            <Button
              type="text"
              icon={<ArrowLeft size={16} />}
              onClick={() => navigate(-1)}
              className="professional-button text-primary hover:bg-primary-100 border-none"
              size="large"
            >
              返回
            </Button>
            
            <div className="h-8 w-px bg-border-primary"></div>
            
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-professional-gradient rounded-lg flex items-center justify-center shadow-professional">
                <Database size={20} className="text-white" />
              </div>
              <div>
                <Title level={3} className="professional-title text-xl m-0">
                  {title}
                </Title>
                <div className="flex items-center space-x-2">
                  <div className="px-2 py-1 bg-accent-primary/20 rounded-md border border-accent-primary/30">
                    <Text className="text-accent-primary text-xs uppercase tracking-wider font-medium">
                      {category}
                    </Text>
                  </div>
                  {subtitle && (
                    <Text className="text-secondary text-sm">
                      {subtitle}
                    </Text>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* 右侧状态 */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success-500 rounded-full"></div>
              <Text className="text-secondary text-sm">
                工具运行中
              </Text>
            </div>
            
            <div className="h-8 w-px bg-border-primary"></div>
            
            <Button
              type="text"
              icon={<Home size={16} />}
              onClick={() => navigate('/dashboard')}
              className="professional-button text-primary hover:bg-primary-100 border-none"
              size="large"
            >
              控制面板
            </Button>
          </div>
        </div>
      </Header>

      {/* 主要内容区域 */}
      <Content className="professional-content">
        <div className="w-full max-w-none px-4 sm:px-6 lg:px-8">
          {/* 工具容器 */}
          <div className="professional-card p-4 sm:p-6 lg:p-8">
            <div className="relative">
              {/* 主要内容 */}
              <div className="relative z-10">
                {children}
              </div>
            </div>
          </div>
        </div>
      </Content>
      
      {/* 底部状态栏 */}
      <div className="glass-panel border-t border-primary-300 px-4 sm:px-6 py-3">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-4 sm:space-x-6">
            <Text className="text-secondary text-sm">
              IDEALAB Professional • {title}
            </Text>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success-500 rounded-full"></div>
              <Text className="text-secondary text-sm">
                系统稳定运行
              </Text>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Text className="text-secondary text-sm">
              版本: v2.0
            </Text>
            <Text className="text-secondary text-sm">
              状态: 在线
            </Text>
          </div>
        </div>
      </div>
    </Layout>
  );
};