import React, { useState } from 'react';
import { Layout, Typography, Button, Statistic, message } from 'antd';
import { BarChart3, TrendingUp, DollarSign, AlertTriangle } from 'lucide-react';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';

const { Sider, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

export const BondAnalysisTool: React.FC = () => {
    const [loading, setLoading] = useState(false);

    const handleAnalyze = () => {
        setLoading(true);
        message.info('债券分析功能正在开发中...');
        setTimeout(() => setLoading(false), 2000);
    };

    return (
        <div style={{ width: '100%', height: 'calc(100vh - 160px)', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%' }}>
                <Sider width={280} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                            <Statistic 
                                title={<Text className="!text-gray-300">债券数量</Text>} 
                                value={0} 
                                prefix={<BarChart3 size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                            <Statistic 
                                title={<Text className="!text-gray-300">分析结果</Text>} 
                                value={0} 
                                prefix={<TrendingUp size={16} />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                        </div>
                        
                        <Title level={5} className="!text-white" style={{ marginTop: '24px' }}>快速操作</Title>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <Button 
                                type="primary" 
                                icon={<BarChart3 size={16} />} 
                                onClick={handleAnalyze}
                                loading={loading}
                                style={{ width: '100%' }}
                            >
                                开始分析
                            </Button>
                            <Button 
                                icon={<DollarSign size={16} />} 
                                style={{ width: '100%' }} 
                                disabled
                            >
                                导入数据
                            </Button>
                        </div>
                    </GlassmorphicCard>
                </Sider>
                
                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0, height: '100%' }}>
                        <div style={{ 
                            padding: '48px', 
                            height: '100%', 
                            display: 'flex', 
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                        }}>
                            <BarChart3 size={64} style={{ color: '#22d3ee', marginBottom: '24px' }} />
                            <Title level={3} className="!text-white" style={{ marginBottom: '16px' }}>
                                债券市场分析工具
                            </Title>
                            <Paragraph style={{ 
                                color: 'rgba(255, 255, 255, 0.7)', 
                                fontSize: '16px',
                                maxWidth: '400px',
                                lineHeight: 1.6
                            }}>
                                专业的债券市场数据分析工具，提供投资建议和风险评估。
                                该功能正在开发中，敬请期待。
                            </Paragraph>
                            <div style={{ 
                                display: 'flex', 
                                gap: '24px', 
                                marginTop: '32px',
                                flexWrap: 'wrap',
                                justifyContent: 'center'
                            }}>
                                <div style={{
                                    background: 'rgba(255, 255, 255, 0.05)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: '12px',
                                    padding: '20px',
                                    width: '160px',
                                    textAlign: 'center'
                                }}>
                                    <TrendingUp size={32} style={{ color: '#10b981', marginBottom: '12px' }} />
                                    <Text className="!text-white" style={{ display: 'block', fontWeight: 600 }}>
                                        收益率分析
                                    </Text>
                                    <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '12px' }}>
                                        即将推出
                                    </Text>
                                </div>
                                <div style={{
                                    background: 'rgba(255, 255, 255, 0.05)',
                                    border: '1px solid rgba(255, 255, 255, 0.1)',
                                    borderRadius: '12px',
                                    padding: '20px',
                                    width: '160px',
                                    textAlign: 'center'
                                }}>
                                    <AlertTriangle size={32} style={{ color: '#f97316', marginBottom: '12px' }} />
                                    <Text className="!text-white" style={{ display: 'block', fontWeight: 600 }}>
                                        风险评估
                                    </Text>
                                    <Text style={{ color: 'rgba(255, 255, 255, 0.6)', fontSize: '12px' }}>
                                        即将推出
                                    </Text>
                                </div>
                            </div>
                        </div>
                    </GlassmorphicCard>
                </Content>
            </Layout>
        </div>
    );
};