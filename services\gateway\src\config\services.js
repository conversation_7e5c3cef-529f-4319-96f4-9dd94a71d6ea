const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');

// 服务配置
const SERVICE_CONFIGS = {
  auth: {
    url: process.env.AUTH_SERVICE_URL || 'http://localhost:8001',
    timeout: 10000,
    retries: 3
  },
  nlp: {
    url: process.env.NLP_SERVICE_URL || 'http://localhost:8002',
    timeout: 30000,
    retries: 2
  },
  financial: {
    url: process.env.FINANCIAL_SERVICE_URL || 'http://localhost:8003',
    timeout: 30000,
    retries: 2
  },
  document: {
    url: process.env.DOCUMENT_SERVICE_URL || 'http://localhost:8004',
    timeout: 60000,
    retries: 1
  }
};

// 权限映射
const PERMISSION_ROUTES = {
  // NLP服务权限
  '/api/nlp/chat': ['chatbot:use'],
  '/api/nlp/sentiment': ['sentiment:analyze'],
  
  // 金融服务权限
  '/api/financial/equity-penetration': ['equity:penetration'],
  '/api/financial/equity-pledge': ['equity:pledge'],
  '/api/financial/bond-analysis': ['bond:analyze'],
  
  // 文档服务权限
  '/api/document/ocr': ['document:ocr'],
  '/api/document/watermark': ['document:watermark'],
  '/api/document/excel-merge': ['document:excel']
};

// 不需要认证的路由
const PUBLIC_ROUTES = [
  '/health',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh'
];

// 不同服务的限流配置
const createServiceLimiter = (serviceName) => {
  const configs = {
    auth: { windowMs: 15 * 60 * 1000, max: 20 }, // 认证服务更严格
    nlp: { windowMs: 15 * 60 * 1000, max: 50 },
    financial: { windowMs: 15 * 60 * 1000, max: 30 },
    document: { windowMs: 15 * 60 * 1000, max: 10 } // 文档服务最严格
  };

  const config = configs[serviceName] || { windowMs: 15 * 60 * 1000, max: 100 };
  
  return rateLimit({
    windowMs: config.windowMs,
    max: config.max,
    message: {
      success: false,
      message: `Too many requests to ${serviceName} service, please try again later.`
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // 为认证用户和匿名用户使用不同的限流策略
      const userId = req.user?.userId || 'anonymous';
      const ip = req.ip || req.connection.remoteAddress;
      return `${serviceName}:${userId}:${ip}`;
    }
  });
};

// 创建代理中间件
const createServiceProxy = (serviceName) => {
  const config = SERVICE_CONFIGS[serviceName];
  
  return createProxyMiddleware({
    target: config.url,
    changeOrigin: true,
    timeout: config.timeout,
    pathRewrite: {
      '^/api': ''
    },
    onError: (err, req, res) => {
      console.error(`Proxy error for ${serviceName}:`, err);
      res.status(502).json({
        success: false,
        message: `Service ${serviceName} temporarily unavailable`,
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
      });
    },
    onProxyReq: (proxyReq, req, res) => {
      // 添加用户信息到请求头
      if (req.user) {
        proxyReq.setHeader('X-User-ID', req.user.userId);
        proxyReq.setHeader('X-User-Role', req.user.role);
        // 安全地处理可能不存在的 permissions 字段
        const permissions = req.user.permissions || [];
        proxyReq.setHeader('X-User-Permissions', JSON.stringify(permissions));
      }
      
      // 添加请求追踪ID
      proxyReq.setHeader('X-Request-ID', req.id || generateRequestId());
    },
    onProxyRes: (proxyRes, req, res) => {
      // 添加响应头
      proxyRes.headers['X-Service'] = serviceName;
      proxyRes.headers['X-Gateway-Version'] = '1.0.0';
    }
  });
};

// 生成请求ID
const generateRequestId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 服务健康检查
const healthCheck = async (serviceName) => {
  const config = SERVICE_CONFIGS[serviceName];
  
  try {
    // 使用简单的HTTP请求检查，而不是fetch
    const http = require('http');
    const url = new URL(`${config.url}/health`);
    
    return new Promise((resolve) => {
      const req = http.request({
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'GET',
        timeout: 5000
      }, (res) => {
        resolve(res.statusCode === 200);
      });
      
      req.on('error', () => {
        resolve(false);
      });
      
      req.on('timeout', () => {
        resolve(false);
      });
      
      req.end();
    });
  } catch (error) {
    console.error(`Health check failed for ${serviceName}:`, error);
    return false;
  }
};

module.exports = {
  SERVICE_CONFIGS,
  PERMISSION_ROUTES,
  PUBLIC_ROUTES,
  createServiceLimiter,
  createServiceProxy,
  healthCheck
};