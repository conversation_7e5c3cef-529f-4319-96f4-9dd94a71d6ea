@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Manrope:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 专业数据实验室主色系 */
  --primary-900: #0f172a;
  --primary-800: #1e293b;
  --primary-700: #334155;
  --primary-600: #475569;
  --primary-500: #64748b;
  --primary-400: #94a3b8;
  --primary-300: #cbd5e1;
  --primary-200: #e2e8f0;
  --primary-100: #f1f5f9;
  --primary-50: #f8fafc;
  
  /* 数据蓝色系 - 专业化 */
  --data-900: #0c1426;
  --data-800: #1e3a8a;
  --data-700: #1e40af;
  --data-600: #2563eb;
  --data-500: #3b82f6;
  --data-400: #60a5fa;
  --data-300: #93c5fd;
  --data-200: #bfdbfe;
  --data-100: #dbeafe;
  
  /* 科技青色系 - 数据可视化 */
  --tech-900: #0a1a1a;
  --tech-800: #164e63;
  --tech-700: #0e7490;
  --tech-600: #0891b2;
  --tech-500: #06b6d4;
  --tech-400: #22d3ee;
  --tech-300: #67e8f9;
  --tech-200: #a5f3fc;
  --tech-100: #cffafe;
  
  /* 成功绿色系 - 数据状态 */
  --success-800: #064e3b;
  --success-700: #047857;
  --success-600: #059669;
  --success-500: #10b981;
  --success-400: #34d399;
  --success-300: #6ee7b7;
  
  /* 警告橙色系 - 数据警告 */
  --warning-800: #9a3412;
  --warning-700: #c2410c;
  --warning-600: #ea580c;
  --warning-500: #f97316;
  --warning-400: #fb923c;
  
  /* 错误红色系 - 数据错误 */
  --error-800: #991b1b;
  --error-700: #b91c1c;
  --error-600: #dc2626;
  --error-500: #ef4444;
  --error-400: #f87171;
  
  /* 中性灰色系 - 界面基础 */
  --neutral-900: #111827;
  --neutral-800: #1f2937;
  --neutral-700: #374151;
  --neutral-600: #4b5563;
  --neutral-500: #6b7280;
  --neutral-400: #9ca3af;
  --neutral-300: #d1d5db;
  --neutral-200: #e5e7eb;
  --neutral-100: #f3f4f6;
  
  /* 专业化强调色 */
  --accent-primary: #2563eb;
  --accent-secondary: #0891b2;
  --accent-tertiary: #7c3aed;
  
  /* 背景色系 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-surface: rgba(255, 255, 255, 0.05);
  --bg-elevated: rgba(255, 255, 255, 0.08);
  
  /* 文字色系 */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.75);
  --text-tertiary: rgba(255, 255, 255, 0.55);
  --text-quaternary: rgba(255, 255, 255, 0.35);
  
  /* 边框色系 */
  --border-primary: rgba(255, 255, 255, 0.15);
  --border-secondary: rgba(255, 255, 255, 0.08);
  --border-accent: rgba(37, 99, 235, 0.3);
}

* {
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  background-attachment: fixed;
  color: var(--text-primary);
  overflow-x: hidden;
  min-height: 100vh;
  height: 100%;
}

#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  height: 100%;
}

/* 专业化卡片效果 */
.professional-card {
  background: 
    linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-surface) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(20px) saturate(1.2);
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.professional-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: var(--border-accent);
}

/* 专业化玻璃形态效果 */
.glass-panel {
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(1.2);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-panel:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 专业化按钮效果 */
.professional-button {
  background: 
    linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: white;
  cursor: pointer;
  box-shadow: 
    0 4px 16px rgba(37, 99, 235, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: none;
  letter-spacing: 0.025em;
}

.professional-button:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 8px 24px rgba(37, 99, 235, 0.4);
  background: 
    linear-gradient(135deg, var(--accent-secondary) 0%, var(--accent-primary) 100%);
}

.professional-button:active {
  transform: translateY(0);
}

/* 专业化输入框效果 */
.professional-input {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.professional-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 
    0 0 20px rgba(37, 99, 235, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.15);
  background: var(--bg-elevated);
}

.professional-input::placeholder {
  color: var(--text-tertiary);
}

/* 专业化加载动画 */
.professional-loader {
  width: 40px;
  height: 40px;
  border: 2px solid var(--border-secondary);
  border-radius: 50%;
  border-top-color: var(--accent-primary);
  animation: professional-spin 1s linear infinite;
}

@keyframes professional-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 专业化标题样式 */
.professional-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  background: 
    linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

/* 专业化工具字体系统 */
.font-display { font-family: 'Inter', sans-serif; }
.font-body { font-family: 'Inter', sans-serif; }
.font-mono { font-family: 'JetBrains Mono', monospace; }

/* 专业化渐变背景 */
.bg-professional-gradient {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}

.bg-data-gradient {
  background: linear-gradient(135deg, var(--data-600) 0%, var(--data-400) 100%);
}

.bg-tech-gradient {
  background: linear-gradient(135deg, var(--tech-600) 0%, var(--tech-400) 100%);
}

/* 专业化阴影效果 */
.shadow-professional {
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.2);
}

.shadow-data {
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.shadow-tech {
  box-shadow: 0 8px 32px rgba(6, 182, 212, 0.2);
}

/* 专业化动画效果 */
.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

@keyframes fade-in {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* 专业化滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: 
    linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: 
    linear-gradient(135deg, var(--accent-secondary) 0%, var(--accent-primary) 100%);
}

/* Ant Design 组件专业化定制 */
.ant-layout {
  background: transparent !important;
}

/* 专业化卡片设计 */
.ant-card {
  background: var(--bg-elevated) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(20px) saturate(1.2) !important;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin-bottom: 24px !important;
  max-width: 100% !important;
}

.ant-card-body {
  color: var(--text-primary) !important;
  padding: 24px !important;
}

.ant-card-head {
  background: var(--bg-surface) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-card-head-title {
  color: var(--text-primary) !important;
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

/* 专业化输入框设计 */
.ant-input, 
.ant-input-affix-wrapper,
.ant-select-selector,
.ant-textarea {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px) !important;
  padding: 10px 16px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

.ant-input-affix-wrapper .ant-input {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: var(--text-primary) !important;
  padding: 0 !important;
}

.ant-input-password {
  background: var(--bg-surface) !important;
}

.ant-input-prefix {
  color: var(--accent-primary) !important;
  margin-right: 8px !important;
}

.ant-input-suffix {
  color: var(--text-tertiary) !important;
}

.ant-input:focus, 
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper:focus-within,
.ant-select-focused .ant-select-selector,
.ant-textarea:focus {
  border-color: var(--accent-primary) !important;
  box-shadow: 
    0 0 20px rgba(37, 99, 235, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.15) !important;
  background: var(--bg-elevated) !important;
}

.ant-input::placeholder,
.ant-textarea::placeholder {
  color: var(--text-tertiary) !important;
}

/* 专业化按钮设计 */
.ant-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-family: 'Inter', sans-serif !important;
  letter-spacing: 0.025em !important;
  min-height: 36px !important;
}

.ant-btn-primary {
  background: var(--accent-primary) !important;
  border: none !important;
  box-shadow: 
    0 4px 16px rgba(37, 99, 235, 0.3) !important;
  color: white !important;
  font-weight: 500 !important;
}

.ant-btn-primary:hover {
  background: var(--accent-secondary) !important;
  transform: translateY(-1px) !important;
  box-shadow: 
    0 8px 24px rgba(37, 99, 235, 0.4) !important;
}

.ant-btn-default {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
  backdrop-filter: blur(10px) !important;
}

.ant-btn-default:hover {
  background: var(--bg-elevated) !important;
  border-color: var(--border-accent) !important;
  transform: translateY(-1px) !important;
  color: var(--text-primary) !important;
}

/* 专业化表格设计 */
.ant-table {
  background: var(--bg-surface) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  backdrop-filter: blur(16px) !important;
  border: 1px solid var(--border-primary) !important;
}

.ant-table-thead > tr > th {
  background: var(--bg-elevated) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  font-weight: 600 !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
  padding: 16px !important;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--border-secondary) !important;
  color: var(--text-secondary) !important;
  padding: 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.ant-table-tbody > tr:hover > td {
  background: var(--bg-elevated) !important;
}

/* 专业化下拉选择框 */
.ant-select-dropdown {
  background: var(--bg-primary) !important;
  backdrop-filter: blur(24px) saturate(1.2) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 8px !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2) !important;
}

.ant-select-item {
  color: var(--text-primary) !important;
  transition: all 0.3s ease !important;
}

.ant-select-item:hover {
  background: var(--bg-elevated) !important;
}

.ant-select-item-option-selected {
  background: var(--accent-primary) !important;
  color: white !important;
}

/* 专业化分页组件 */
.ant-pagination {
  text-align: center !important;
  margin-top: 24px !important;
}

.ant-pagination .ant-pagination-item {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 6px !important;
  color: var(--text-primary) !important;
}

.ant-pagination .ant-pagination-item:hover,
.ant-pagination .ant-pagination-item-active {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
  color: white !important;
}

/* 专业化消息提示 */
.ant-message {
  top: 80px !important;
}

.ant-message-notice-content {
  background: var(--bg-primary) !important;
  backdrop-filter: blur(24px) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 8px !important;
  color: var(--text-primary) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2) !important;
}

/* 专业化模态框 */
.ant-modal-content {
  background: var(--bg-primary) !important;
  backdrop-filter: blur(24px) saturate(1.2) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 12px !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3) !important;
}

.ant-modal-header {
  background: var(--bg-surface) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-modal-title {
  color: var(--text-primary) !important;
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
}

.ant-modal-body {
  color: var(--text-primary) !important;
}

/* 专业化加载指示器 */
.ant-spin-dot {
  color: var(--accent-primary) !important;
}

.ant-spin-dot-item {
  background-color: var(--accent-primary) !important;
}

/* 登录页面特殊优化 */
.login-page .ant-form-item {
  margin-bottom: 20px !important;
}

.login-page .ant-input,
.login-page .ant-input-affix-wrapper {
  height: 48px !important;
  font-size: 16px !important;
  padding: 12px 16px !important;
}

.login-page .ant-input-affix-wrapper .ant-input {
  height: auto !important;
  padding: 0 !important;
}

/* 确保表单元素不被遮挡 */
.ant-form {
  position: relative !important;
  z-index: 10 !important;
}

.ant-form-item {
  position: relative !important;
  z-index: 10 !important;
}

.ant-card .ant-card-body {
  position: relative !important;
  z-index: 10 !important;
}

/* 专业化表单布局 */
.professional-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

.professional-form .ant-row {
  margin-bottom: 24px;
}

.professional-form .ant-col {
  padding: 0 12px;
}

.professional-form .ant-form-item {
  margin-bottom: 20px;
}

.professional-form .ant-form-item-label {
  padding-bottom: 8px;
}

.professional-form .ant-form-item-label > label {
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
}

.professional-form .ant-form-item-required::before {
  color: var(--error-500) !important;
}

/* 专业化网格布局 */
.professional-grid {
  display: grid;
  gap: 24px;
  margin-bottom: 32px;
}

.professional-grid.grid-1 {
  grid-template-columns: 1fr;
}

.professional-grid.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.professional-grid.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.professional-grid.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 专业化内容区域 */
.professional-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
}

.professional-section {
  margin-bottom: 48px;
}

.professional-section:last-child {
  margin-bottom: 0;
}

.professional-section-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
  font-size: 24px !important;
  color: var(--text-primary) !important;
  margin-bottom: 24px !important;
  background: 
    linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.professional-section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 2px;
}

.professional-section-subtitle {
  font-family: 'Inter', sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: var(--text-secondary) !important;
  margin-bottom: 32px !important;
  line-height: 1.6 !important;
}

/* 专业化统计卡片 */
.stat-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(16px) saturate(1.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}

.stat-card:hover {
  transform: translateY(-2px);
  border-color: var(--border-accent);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px var(--border-accent);
}

.stat-card-number {
  font-family: 'Inter', sans-serif !important;
  font-weight: 700 !important;
  font-size: 28px !important;
  color: var(--text-primary) !important;
  margin-bottom: 8px !important;
  background: 
    linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-card-label {
  font-family: 'Inter', sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--text-secondary) !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

/* 专业化操作按钮组 */
.action-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin: 24px 0;
}

.action-group .ant-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.025em;
}

/* 专业化数据展示区域 */
.data-display {
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  backdrop-filter: blur(16px);
}

.data-display-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  color: var(--text-primary) !important;
  margin-bottom: 16px !important;
  letter-spacing: 0.025em !important;
}

.data-display-content {
  color: var(--text-secondary) !important;
  font-family: 'Inter', sans-serif !important;
  line-height: 1.6 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .professional-grid.grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  .professional-grid.grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .professional-grid.grid-4,
  .professional-grid.grid-3,
  .professional-grid.grid-2 {
    grid-template-columns: 1fr;
  }
  
  .professional-form {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .professional-form .ant-col {
    padding: 0 8px;
  }
  
  .action-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-group .ant-btn {
    width: 100%;
    min-width: auto;
  }
  
  .professional-content {
    padding: 24px 16px;
  }
}

/* 移除旧的样式类 */
.text-galaxy-400,
.text-nebula-400,
.text-aurora-400,
.text-cosmic-400,
.text-aurora-300,
.text-aurora-200 {
  color: var(--text-secondary);
}

.border-aurora-500,
.border-galaxy-500 {
  border-color: var(--border-accent);
}

/* 移除旧的动画 */
.animate-drift,
.animate-twinkle,
.animate-stellar-pulse {
  animation: none;
}

/* 现代化登录页面样式 */
.modern-login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.modern-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.modern-background-layers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modern-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

.modern-layer-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(8, 145, 178, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(124, 58, 237, 0.06) 0%, transparent 50%);
}

.modern-layer-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(45deg, transparent 30%, rgba(37, 99, 235, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(8, 145, 178, 0.02) 50%, transparent 70%);
}

.modern-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.modern-float-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(8, 145, 178, 0.05) 100%);
  animation: modernFloat 20s linear infinite;
}

.modern-float-1 {
  width: 120px;
  height: 120px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.modern-float-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 15%;
  animation-delay: -7s;
}

.modern-float-3 {
  width: 60px;
  height: 60px;
  top: 30%;
  right: 30%;
  animation-delay: -14s;
}

.modern-float-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: -10s;
}

@keyframes modernFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(0px) rotate(180deg);
    opacity: 0.3;
  }
  75% {
    transform: translateY(20px) rotate(270deg);
    opacity: 0.6;
  }
}

.modern-top-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(1.2);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  z-index: 1000;
}

.modern-time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.modern-time-text {
  font-family: 'JetBrains Mono', monospace;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.modern-date-text {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.modern-status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-500);
  animation: modernPulse 2s ease-in-out infinite;
}

@keyframes modernPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.modern-status-text {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.modern-login-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 32px 40px;
  z-index: 10;
}

.modern-login-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;
  max-width: 1200px;
  width: 100%;
}

.modern-brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-brand-container {
  text-align: center;
  position: relative;
}

.modern-brand-icon {
  position: relative;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40px;
  color: white;
  box-shadow: 
    0 20px 60px rgba(37, 99, 235, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.2);
  animation: modernIconFloat 6s ease-in-out infinite;
}

@keyframes modernIconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.modern-brand-sparkle {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--tech-400) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: modernSparkle 3s ease-in-out infinite;
}

@keyframes modernSparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

.modern-brand-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 800 !important;
  font-size: 56px !important;
  margin-bottom: 16px !important;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 50%, var(--tech-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(37, 99, 235, 0.3);
  animation: modernTitleGlow 4s ease-in-out infinite;
}

@keyframes modernTitleGlow {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.2);
  }
}

.modern-brand-subtitle {
  font-family: 'Inter', sans-serif !important;
  font-size: 20px !important;
  font-weight: 400 !important;
  color: var(--text-secondary) !important;
  margin-bottom: 40px !important;
  letter-spacing: 0.5px !important;
}

.modern-brand-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.modern-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-feature-item:hover {
  transform: translateY(-2px);
  border-color: var(--accent-primary);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.2);
}

.modern-login-card {
  max-width: 420px;
  width: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
  backdrop-filter: blur(24px) saturate(1.2) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 20px !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  animation: modernCardSlide 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modernCardSlide {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-login-form {
  padding: 48px 40px;
}

.modern-form-header {
  text-align: center;
  margin-bottom: 40px;
}

.modern-form-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 700 !important;
  font-size: 28px !important;
  color: var(--text-primary) !important;
  margin-bottom: 8px !important;
}

.modern-form-subtitle {
  font-family: 'Inter', sans-serif !important;
  font-size: 16px !important;
  color: var(--text-secondary) !important;
  font-weight: 400 !important;
}

.modern-form {
  margin-bottom: 32px;
}

.modern-form .ant-form-item {
  margin-bottom: 24px;
}

.modern-input {
  height: 52px !important;
  border-radius: 12px !important;
  border: 1px solid var(--border-primary) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
  backdrop-filter: blur(10px) !important;
  font-size: 16px !important;
  font-family: 'Inter', sans-serif !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.modern-input:focus,
.modern-input:focus-within {
  border-color: var(--accent-primary) !important;
  box-shadow: 
    0 0 0 3px rgba(37, 99, 235, 0.1),
    0 8px 24px rgba(37, 99, 235, 0.2) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) !important;
}

.modern-button {
  height: 52px !important;
  border-radius: 12px !important;
  border: none !important;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  font-family: 'Inter', sans-serif !important;
  box-shadow: 
    0 8px 24px rgba(37, 99, 235, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

.modern-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 
    0 12px 32px rgba(37, 99, 235, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.15) !important;
  background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--tech-400) 100%) !important;
}

.modern-button:active {
  transform: translateY(0) !important;
}

.modern-demo-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
}

.modern-demo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.modern-demo-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  color: var(--text-primary) !important;
}

.modern-demo-button {
  color: var(--accent-primary) !important;
  padding: 4px 12px !important;
  height: auto !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.modern-demo-button:hover {
  background: rgba(37, 99, 235, 0.1) !important;
  color: var(--accent-primary) !important;
}

.modern-demo-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modern-demo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.modern-demo-label {
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
  color: var(--text-secondary) !important;
}

.modern-demo-value {
  font-family: 'JetBrains Mono', monospace !important;
  font-size: 14px !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  padding: 4px 8px !important;
  background: rgba(37, 99, 235, 0.1) !important;
  border-radius: 4px !important;
}

.modern-system-info {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid var(--border-secondary);
}

.modern-system-text {
  font-family: 'Inter', sans-serif !important;
  font-size: 12px !important;
  color: var(--text-tertiary) !important;
  font-weight: 400 !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .modern-login-wrapper {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .modern-brand-section {
    order: 2;
  }
  
  .modern-brand-title {
    font-size: 48px !important;
  }
  
  .modern-brand-subtitle {
    font-size: 18px !important;
  }
}

@media (max-width: 768px) {
  .modern-login-content {
    padding: 80px 24px 40px;
  }
  
  .modern-login-wrapper {
    gap: 40px;
  }
  
  .modern-brand-section {
    display: none;
  }
  
  .modern-login-card {
    max-width: 100%;
  }
  
  .modern-login-form {
    padding: 40px 32px;
  }
  
  .modern-top-bar {
    padding: 0 20px;
  }
  
  .modern-time-display {
    display: none;
  }
}

@media (max-width: 480px) {
  .modern-login-content {
    padding: 80px 16px 40px;
  }
  
  .modern-login-form {
    padding: 32px 24px;
  }
  
  .modern-form-title {
    font-size: 24px !important;
  }
  
  .modern-form-subtitle {
    font-size: 14px !important;
  }
  
  .modern-top-bar {
    padding: 0 16px;
  }
}

/* 专业登录页面样式 */
.professional-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  position: relative;
}

.professional-login-content {
  width: 100%;
  max-width: 1200px;
  z-index: 10;
  position: relative;
}

.professional-login-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 80vh;
}

/* 品牌区域 */
.professional-brand-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  justify-content: center;
}

.professional-brand-icon {
  width: 80px;
  height: 80px;
  background: var(--bg-professional-gradient);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  color: white;
  box-shadow: var(--shadow-professional);
}

.professional-brand-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 700 !important;
  font-size: 48px !important;
  margin-bottom: 16px !important;
  background: var(--bg-professional-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.professional-brand-subtitle {
  font-family: 'Inter', sans-serif !important;
  font-size: 20px !important;
  color: var(--text-secondary) !important;
  margin-bottom: 48px !important;
}

/* 登录卡片 */
.professional-login-card {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.professional-login-form {
  padding: 40px 32px;
}

.professional-form-header {
  text-align: center;
  margin-bottom: 32px;
}

.professional-form-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 600 !important;
  font-size: 24px !important;
  color: var(--text-primary) !important;
  margin-bottom: 8px !important;
}

.professional-form-subtitle {
  font-family: 'Inter', sans-serif !important;
  font-size: 16px !important;
  color: var(--text-secondary) !important;
}

/* 演示账号区域 */
.professional-demo-section {
  margin-top: 32px;
  padding: 20px;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.professional-demo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.professional-demo-title {
  font-family: 'Inter', sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--text-secondary) !important;
}

.professional-demo-button {
  color: var(--accent-primary) !important;
  padding: 0 !important;
  height: auto !important;
  font-size: 14px !important;
}

.professional-demo-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.professional-demo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.professional-demo-label {
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
  color: var(--text-tertiary) !important;
}

.professional-demo-value {
  font-family: 'JetBrains Mono', monospace !important;
  font-size: 14px !important;
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

/* 系统信息 */
.professional-system-info {
  text-align: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--border-secondary);
}

.professional-system-text {
  font-family: 'Inter', sans-serif !important;
  font-size: 12px !important;
  color: var(--text-quaternary) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .professional-login-wrapper {
    grid-template-columns: 1fr;
    gap: 40px;
    min-height: auto;
  }
  
  .professional-brand-section {
    display: none;
  }
  
  .professional-login-card {
    max-width: 100%;
  }
  
  .professional-login-form {
    padding: 32px 24px;
  }
  
  .professional-brand-title {
    font-size: 36px !important;
  }
  
  .professional-brand-subtitle {
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .professional-login-container {
    padding: 16px;
  }
  
  .professional-login-form {
    padding: 24px 20px;
  }
  
  .professional-form-title {
    font-size: 20px !important;
  }
  
  .professional-form-subtitle {
    font-size: 14px !important;
  }
}

/* 确保兼容性 */
.glass-morphism {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(20px) saturate(1.2);
}

.tool-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(20px) saturate(1.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-card:hover {
  transform: translateY(-2px);
  border-color: var(--border-accent);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px var(--border-accent);
}

/* 专业化图标样式 */
.text-accent-primary {
  color: var(--accent-primary);
}

.text-accent-secondary {
  color: var(--accent-secondary);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-success-500 {
  color: var(--success-500);
}

.text-data-500 {
  color: var(--data-500);
}

.bg-primary-700 {
  background-color: var(--primary-700);
}

.bg-primary-100 {
  background-color: var(--primary-100);
}

.bg-data-500 {
  background-color: var(--data-500);
}

.bg-success-500 {
  background-color: var(--success-500);
}

.border-primary-300 {
  border-color: var(--primary-300);
}

.hover\:bg-primary-100:hover {
  background-color: var(--primary-100);
}

.bg-border-primary {
  background-color: var(--border-primary);
}

.border-accent-primary\/30 {
  border-color: rgba(37, 99, 235, 0.3);
}

.bg-accent-primary\/20 {
  background-color: rgba(37, 99, 235, 0.2);
}

.text-accent-primary {
  color: var(--accent-primary);
}

/* 增强的工具类 */
.bg-surface {
  background-color: var(--bg-surface);
}

.bg-bg-elevated {
  background-color: var(--bg-elevated);
}

.bg-bg-surface {
  background-color: var(--bg-surface);
}

.border-border-primary {
  border-color: var(--border-primary);
}

.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-gradient-professional {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}

/* 登录页面动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(40px); }
  to { opacity: 1; transform: translateX(0); }
}

/* 现代化Dashboard页面样式 */
.modern-dashboard-layout {
  min-height: 100vh;
  background: transparent;
  position: relative;
}

.modern-dashboard-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.modern-dashboard-layers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modern-dashboard-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 60%, #0f172a 100%);
}

.modern-dashboard-layer-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 25% 25%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(8, 145, 178, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 50% 0%, rgba(124, 58, 237, 0.04) 0%, transparent 50%);
}

.modern-dashboard-layer-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(37, 99, 235, 0.02) 50%, transparent 100%),
    linear-gradient(180deg, transparent 0%, rgba(8, 145, 178, 0.015) 50%, transparent 100%);
}

.modern-dashboard-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.modern-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(37, 99, 235, 0.4);
  border-radius: 50%;
  animation: modernParticleFloat 30s linear infinite;
}

@keyframes modernParticleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.modern-dashboard-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) \!important;
  backdrop-filter: blur(20px) saturate(1.2) \!important;
  border-bottom: 1px solid var(--border-primary) \!important;
  padding: 0 \!important;
  height: 80px \!important;
  position: sticky \!important;
  top: 0 \!important;
  z-index: 1000 \!important;
}

.modern-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 32px;
  max-width: 1600px;
  margin: 0 auto;
}

.modern-header-brand {
  display: flex;
  align-items: center;
}

.modern-logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modern-logo {
  position: relative;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
  animation: modernLogoFloat 4s ease-in-out infinite;
}

@keyframes modernLogoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

.modern-logo-sparkle {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--tech-400) 0%, var(--tech-300) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  animation: modernLogoSparkle 2s ease-in-out infinite;
}

@keyframes modernLogoSparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
}

.modern-brand-info {
  display: flex;
  flex-direction: column;
}

.modern-brand-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 700 \!important;
  font-size: 20px \!important;
  margin: 0 \!important;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-brand-subtitle {
  font-family: 'Inter', sans-serif \!important;
  font-size: 12px \!important;
  color: var(--text-secondary) \!important;
  font-weight: 500 \!important;
  margin: 0 \!important;
}

.modern-header-center {
  display: flex;
  align-items: center;
  gap: 24px;
}

.modern-time-widget {
  display: flex;
  align-items: center;
}

.modern-time-display {
  text-align: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.modern-time-text {
  font-family: 'JetBrains Mono', monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.modern-date-text {
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.modern-search-container {
  position: relative;
}

.modern-search-input {
  width: 320px \!important;
  height: 40px \!important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) \!important;
  border: 1px solid var(--border-primary) \!important;
  border-radius: 10px \!important;
  backdrop-filter: blur(10px) \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
}

.modern-search-input:focus,
.modern-search-input:focus-within {
  border-color: var(--accent-primary) \!important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) \!important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) \!important;
}

.modern-header-user {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modern-status-widget {
  display: flex;
  align-items: center;
}

.modern-online-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.modern-online-dot {
  width: 6px;
  height: 6px;
  background: var(--success-500);
  border-radius: 50%;
  animation: modernOnlinePulse 2s ease-in-out infinite;
}

@keyframes modernOnlinePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.modern-online-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 12px \!important;
  color: var(--text-secondary) \!important;
  font-weight: 500 \!important;
}

.modern-user-button {
  display: flex \!important;
  align-items: center \!important;
  gap: 12px \!important;
  padding: 8px 16px \!important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) \!important;
  border: 1px solid var(--border-primary) \!important;
  border-radius: 10px \!important;
  backdrop-filter: blur(10px) \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
}

.modern-user-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) \!important;
  border-color: var(--accent-primary) \!important;
  transform: translateY(-1px) \!important;
}

.modern-user-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.modern-user-name {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.modern-user-role {
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.modern-dashboard-content {
  padding: 40px 32px \!important;
  min-height: calc(100vh - 80px - 60px) \!important;
}

.modern-content-container {
  max-width: 1600px;
  margin: 0 auto;
}

.modern-welcome-section {
  margin-bottom: 48px;
}

.modern-welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.modern-welcome-text {
  flex: 1;
}

.modern-welcome-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 700 \!important;
  font-size: 32px \!important;
  color: var(--text-primary) \!important;
  margin-bottom: 8px \!important;
}

.modern-welcome-subtitle {
  font-family: 'Inter', sans-serif \!important;
  font-size: 16px \!important;
  color: var(--text-secondary) \!important;
  font-weight: 400 \!important;
  line-height: 1.6 \!important;
}

.modern-welcome-widgets {
  display: flex;
  gap: 16px;
}

.modern-widget {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-widget:hover {
  transform: translateY(-2px);
  border-color: var(--accent-primary);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);
}

.modern-widget-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.modern-widget-text {
  display: flex;
  flex-direction: column;
}

.modern-widget-title {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  line-height: 1.2;
}

.modern-widget-value {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.modern-dashboard-stats {
  margin-bottom: 32px;
}

.modern-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.modern-stat-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px) saturate(1.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
}

.modern-stat-card:hover {
  transform: translateY(-4px);
  border-color: var(--accent-primary);
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.2);
}

.modern-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modern-stat-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.modern-stat-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
}

.modern-stat-content {
  margin-bottom: 16px;
}

.modern-stat-value {
  font-family: 'Inter', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
  margin-bottom: 4px;
}

.modern-stat-label {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  line-height: 1.2;
}

.modern-stat-progress {
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.modern-progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 1s ease-in-out;
  position: relative;
  overflow: hidden;
}

.modern-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: modernProgressShine 2s infinite;
}

@keyframes modernProgressShine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.modern-tools-section {
  margin-bottom: 32px;
}

.modern-section-header {
  margin-bottom: 24px;
}

.modern-section-title-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modern-section-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 700 \!important;
  font-size: 24px \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
}

.modern-section-badge {
  padding: 6px 12px;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(8, 145, 178, 0.1) 100%);
  border: 1px solid rgba(37, 99, 235, 0.2);
  border-radius: 16px;
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: var(--accent-primary);
  backdrop-filter: blur(10px);
}

.modern-category-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 32px;
}

.modern-category-button {
  height: 36px \!important;
  padding: 0 16px \!important;
  border-radius: 8px \!important;
  font-family: 'Inter', sans-serif \!important;
  font-size: 13px \!important;
  font-weight: 500 \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
}

.modern-tools-grid {
  margin-bottom: 40px;
}

.modern-tool-card {
  height: 100% \!important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) \!important;
  border: 1px solid var(--border-primary) \!important;
  border-radius: 16px \!important;
  backdrop-filter: blur(20px) saturate(1.2) \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
  cursor: pointer \!important;
  overflow: hidden \!important;
}

.modern-tool-card:hover {
  transform: translateY(-6px) \!important;
  border-color: var(--accent-primary) \!important;
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.2) \!important;
}

.modern-tool-card-content {
  padding: 24px \!important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modern-tool-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.modern-tool-icon-container {
  position: relative;
}

.modern-tool-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
  position: relative;
  overflow: hidden;
}

.modern-tool-emoji {
  font-size: 24px;
  line-height: 1;
}

.modern-tool-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-tool-card:hover .modern-tool-glow {
  opacity: 1;
}

.modern-tool-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.modern-featured-badge {
  background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--tech-400) 100%) \!important;
  border: none \!important;
  color: white \!important;
  padding: 2px 6px \!important;
  border-radius: 6px \!important;
  font-size: 10px \!important;
  font-weight: 600 \!important;
  box-shadow: 0 2px 8px rgba(8, 145, 178, 0.3) \!important;
}

.modern-tool-category {
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  backdrop-filter: blur(5px);
}

.modern-tool-info {
  flex: 1;
  margin-bottom: 20px;
}

.modern-tool-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 600 \!important;
  font-size: 16px \!important;
  color: var(--text-primary) \!important;
  margin: 0 0 8px 0 \!important;
  line-height: 1.3 \!important;
}

.modern-tool-description {
  font-family: 'Inter', sans-serif \!important;
  font-size: 13px \!important;
  color: var(--text-secondary) \!important;
  line-height: 1.5 \!important;
  margin: 0 \!important;
}

.modern-tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--border-secondary);
}

.modern-tool-status {
  display: flex;
  align-items: center;
}

.modern-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.modern-status-dot-active {
  width: 6px;
  height: 6px;
  background: var(--success-500);
  border-radius: 50%;
  animation: modernStatusPulse 2s ease-in-out infinite;
}

@keyframes modernStatusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.modern-status-text {
  font-family: 'Inter', sans-serif;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-tool-arrow {
  color: var(--text-tertiary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-arrow-active {
  color: var(--accent-primary);
  transform: translateX(2px);
}

.modern-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
}

.modern-empty-container {
  text-align: center;
  padding: 48px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  max-width: 400px;
}

.modern-empty-icon {
  font-size: 48px;
  margin-bottom: 24px;
  opacity: 0.7;
}

.modern-empty-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 600 \!important;
  font-size: 20px \!important;
  color: var(--text-primary) \!important;
  margin: 0 0 12px 0 \!important;
}

.modern-empty-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 14px \!important;
  color: var(--text-secondary) \!important;
  line-height: 1.6 \!important;
  margin: 0 \!important;
}

.modern-dashboard-footer {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  backdrop-filter: blur(20px) saturate(1.2);
  border-top: 1px solid var(--border-primary);
  padding: 16px 32px;
  height: 60px;
  display: flex;
  align-items: center;
}

.modern-footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
}

.modern-footer-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.modern-footer-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 13px \!important;
  color: var(--text-secondary) \!important;
  font-weight: 500 \!important;
}

.modern-footer-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-footer-dot {
  width: 6px;
  height: 6px;
  background: var(--success-500);
  border-radius: 50%;
  animation: modernFooterPulse 2s ease-in-out infinite;
}

@keyframes modernFooterPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.modern-footer-status-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 12px \!important;
  color: var(--success-500) \!important;
  font-weight: 500 \!important;
}

.modern-footer-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.modern-footer-info {
  font-family: 'Inter', sans-serif \!important;
  font-size: 12px \!important;
  color: var(--text-tertiary) \!important;
  font-weight: 500 \!important;
}

/* Dashboard 响应式设计 */
@media (max-width: 1200px) {
  .modern-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .modern-welcome-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
  }
  
  .modern-welcome-widgets {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .modern-header-content {
    padding: 0 20px;
  }
  
  .modern-header-center {
    gap: 16px;
  }
  
  .modern-time-widget {
    display: none;
  }
  
  .modern-search-input {
    width: 240px \!important;
  }
  
  .modern-welcome-widgets {
    display: none;
  }
  
  .modern-dashboard-content {
    padding: 24px 20px \!important;
  }
  
  .modern-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modern-category-buttons {
    flex-wrap: wrap;
  }
  
  .modern-footer-content {
    padding: 0 20px;
  }
  
  .modern-footer-right {
    display: none;
  }
}

@media (max-width: 480px) {
  .modern-header-content {
    padding: 0 16px;
  }
  
  .modern-search-input {
    width: 180px \!important;
  }
  
  .modern-user-info {
    display: none;
  }
  
  .modern-dashboard-content {
    padding: 20px 16px \!important;
  }
  
  .modern-welcome-title {
    font-size: 24px \!important;
  }
  
  .modern-welcome-subtitle {
    font-size: 14px \!important;
  }
  
  .modern-tool-card-content {
    padding: 20px \!important;
  }
  
  .modern-footer-content {
    padding: 0 16px;
  }
}


/* 专业化表格样式 */
.professional-table .ant-table {
  background: var(--bg-surface) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  backdrop-filter: blur(16px) !important;
  border: 1px solid var(--border-primary) !important;
}

.professional-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(8, 145, 178, 0.1) 100%) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-primary) !important;
  font-weight: 600 !important;
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
  padding: 16px !important;
  text-align: left !important;
}

.professional-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--border-secondary) !important;
  color: var(--text-secondary) !important;
  padding: 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  font-family: 'Inter', sans-serif !important;
}

.professional-table .ant-table-tbody > tr:hover > td {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(8, 145, 178, 0.05) 100%) !important;
  color: var(--text-primary) !important;
}

.professional-table .ant-table-tbody > tr.ant-table-row-selected > td {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(8, 145, 178, 0.1) 100%) !important;
  color: var(--text-primary) !important;
}

.professional-table .ant-pagination {
  margin-top: 24px !important;
  text-align: center !important;
}

.professional-table .ant-pagination .ant-pagination-item {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 6px !important;
  color: var(--text-primary) !important;
  font-family: 'Inter', sans-serif !important;
}

.professional-table .ant-pagination .ant-pagination-item:hover,
.professional-table .ant-pagination .ant-pagination-item-active {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
  color: white !important;
}

.professional-table .ant-pagination .ant-pagination-prev,
.professional-table .ant-pagination .ant-pagination-next {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.professional-table .ant-pagination .ant-pagination-prev:hover,
.professional-table .ant-pagination .ant-pagination-next:hover {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
  color: white !important;
}

/* 专业化Tabs组件样式 */
.professional-tabs .ant-tabs-nav {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
  border: 1px solid var(--border-primary) !important;
  border-radius: 12px 12px 0 0 !important;
  margin-bottom: 0 !important;
  padding: 0 24px !important;
  backdrop-filter: blur(10px) !important;
}

.professional-tabs .ant-tabs-tab {
  background: transparent !important;
  border: none !important;
  padding: 16px 24px !important;
  margin: 0 !important;
  font-family: 'Inter', sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: var(--text-secondary) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 8px !important;
  position: relative !important;
}

.professional-tabs .ant-tabs-tab:hover {
  color: var(--accent-primary) !important;
  background: rgba(37, 99, 235, 0.08) !important;
}

.professional-tabs .ant-tabs-tab-active {
  color: var(--accent-primary) !important;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(8, 145, 178, 0.1) 100%) !important;
  border: 1px solid rgba(37, 99, 235, 0.2) !important;
  font-weight: 600 !important;
}

.professional-tabs .ant-tabs-tab-active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 2px;
}

.professional-tabs .ant-tabs-ink-bar {
  display: none !important;
}

.professional-tabs .ant-tabs-content-holder {
  background: var(--bg-surface) !important;
  border: 1px solid var(--border-primary) !important;
  border-top: none !important;
  border-radius: 0 0 12px 12px !important;
  backdrop-filter: blur(16px) !important;
}

.professional-tabs .ant-tabs-content {
  background: transparent !important;
}

.professional-tabs .ant-tabs-tabpane {
  background: transparent !important;
  color: var(--text-primary) !important;
}

/* 现代化聊天界面样式 */
.modern-chat-container {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 600px;
  overflow: hidden;
}

.modern-chat-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.modern-chat-layers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modern-chat-layer-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #0f172a 100%);
}

.modern-chat-layer-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 30%, rgba(37, 99, 235, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(8, 145, 178, 0.05) 0%, transparent 50%);
}

.modern-chat-layer-3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(37, 99, 235, 0.02) 50%, transparent 100%),
    linear-gradient(180deg, transparent 0%, rgba(8, 145, 178, 0.01) 50%, transparent 100%);
}

.modern-chat-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.modern-chat-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(37, 99, 235, 0.3);
  border-radius: 50%;
  animation: modernChatParticleFloat 20s linear infinite;
}

@keyframes modernChatParticleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.modern-chat-layout {
  position: relative;
  z-index: 10;
  display: flex;
  gap: 24px;
  height: 100%;
}

.modern-chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  backdrop-filter: blur(20px) saturate(1.2);
  overflow: hidden;
}

.modern-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
}

.modern-chat-title-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modern-chat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
}

.modern-chat-title-info {
  display: flex;
  flex-direction: column;
}

.modern-chat-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 600 \!important;
  font-size: 20px \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
  line-height: 1.2 \!important;
}

.modern-chat-subtitle {
  font-family: 'Inter', sans-serif \!important;
  font-size: 14px \!important;
  color: var(--text-secondary) \!important;
  font-weight: 500 \!important;
  margin: 0 \!important;
  line-height: 1.2 \!important;
}

.modern-chat-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.modern-chat-messages {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modern-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.modern-message-container {
  margin-bottom: 24px;
}

.modern-message-container.user-message {
  display: flex;
  justify-content: flex-end;
}

.modern-message-container.bot-message {
  display: flex;
  justify-content: flex-start;
}

.modern-message-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  max-width: 80%;
}

.modern-message-container.user-message .modern-message-content {
  flex-direction: row-reverse;
}

.modern-message-avatar {
  position: relative;
  flex-shrink: 0;
}

.modern-avatar {
  position: relative;
  overflow: hidden;
}

.modern-avatar.user-avatar {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) \!important;
  color: white \!important;
}

.modern-avatar.bot-avatar {
  background: linear-gradient(135deg, var(--tech-600) 0%, var(--tech-400) 100%) \!important;
  color: white \!important;
}

.modern-avatar-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(8, 145, 178, 0.2) 100%);
  border-radius: 50%;
  animation: modernAvatarGlow 3s ease-in-out infinite;
  z-index: -1;
}

@keyframes modernAvatarGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

.modern-message-bubble {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.modern-bubble-content {
  padding: 16px 20px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.modern-bubble-content.user-bubble {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  color: white;
  border-color: rgba(37, 99, 235, 0.3);
}

.modern-bubble-content.bot-bubble {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  color: var(--text-primary);
}

.modern-message-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 15px \!important;
  line-height: 1.6 \!important;
  margin: 0 \!important;
}

.modern-message-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0.7;
}

.modern-message-time {
  font-family: 'Inter', sans-serif \!important;
  font-size: 12px \!important;
  color: var(--text-tertiary) \!important;
  margin: 0 \!important;
}

.modern-typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.modern-typing-dot {
  width: 8px;
  height: 8px;
  background: var(--accent-primary);
  border-radius: 50%;
  animation: modernTypingDot 1.4s infinite;
}

.modern-typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.modern-typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.modern-typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes modernTypingDot {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.modern-chat-input-container {
  padding: 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-top: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
}

.modern-input-wrapper {
  position: relative;
}

.modern-input-field {
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-input-field:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.modern-textarea {
  flex: 1;
  background: transparent \!important;
  border: none \!important;
  outline: none \!important;
  resize: none \!important;
  color: var(--text-primary) \!important;
  font-family: 'Inter', sans-serif \!important;
  font-size: 15px \!important;
  line-height: 1.6 \!important;
  padding: 0 \!important;
  min-height: 24px \!important;
  box-shadow: none \!important;
}

.modern-textarea::placeholder {
  color: var(--text-tertiary) \!important;
}

.modern-input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.modern-voice-button {
  width: 36px \!important;
  height: 36px \!important;
  border-radius: 8px \!important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) \!important;
  border: 1px solid var(--border-primary) \!important;
  color: var(--text-secondary) \!important;
  display: flex \!important;
  align-items: center \!important;
  justify-content: center \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
}

.modern-voice-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) \!important;
  border-color: var(--accent-primary) \!important;
  color: var(--accent-primary) \!important;
}

.modern-voice-button.listening {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) \!important;
  border-color: var(--accent-primary) \!important;
  color: white \!important;
}

.modern-send-button {
  width: 36px \!important;
  height: 36px \!important;
  border-radius: 8px \!important;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%) \!important;
  border: none \!important;
  color: white \!important;
  display: flex \!important;
  align-items: center \!important;
  justify-content: center \!important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) \!important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) \!important;
}

.modern-send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--accent-secondary) 0%, var(--tech-400) 100%) \!important;
  transform: translateY(-1px) \!important;
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) \!important;
}

.modern-send-button:disabled {
  opacity: 0.5 \!important;
  cursor: not-allowed \!important;
}

.modern-chat-sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.modern-stats-card,
.modern-features-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) \!important;
  border: 1px solid var(--border-primary) \!important;
  border-radius: 16px \!important;
  backdrop-filter: blur(20px) saturate(1.2) \!important;
  overflow: hidden \!important;
}

.modern-stats-header,
.modern-features-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
}

.modern-stats-title,
.modern-features-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 600 \!important;
  font-size: 16px \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
}

.modern-stats-content,
.modern-features-content {
  padding: 20px;
}

.modern-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-secondary);
}

.modern-stat-item:last-child {
  border-bottom: none;
}

.modern-stat-label {
  font-family: 'Inter', sans-serif \!important;
  font-size: 14px \!important;
  color: var(--text-secondary) \!important;
  margin: 0 \!important;
}

.modern-stat-value {
  font-family: 'Inter', sans-serif \!important;
  font-size: 16px \!important;
  font-weight: 600 \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
}

.modern-quick-actions {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  backdrop-filter: blur(20px) saturate(1.2);
  overflow: hidden;
}

.modern-quick-actions-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(10px);
}

.modern-quick-actions-title {
  font-family: 'Inter', sans-serif \!important;
  font-weight: 600 \!important;
  font-size: 16px \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
}

.modern-actions-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.modern-action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-action-item:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);
}

.modern-action-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.modern-action-text {
  font-family: 'Inter', sans-serif \!important;
  font-size: 14px \!important;
  font-weight: 500 \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
  line-height: 1.4 \!important;
}

.modern-feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-secondary);
}

.modern-feature-item:last-child {
  border-bottom: none;
}

.modern-feature-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-primary);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.modern-feature-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.modern-feature-title {
  font-family: 'Inter', sans-serif \!important;
  font-size: 14px \!important;
  font-weight: 600 \!important;
  color: var(--text-primary) \!important;
  margin: 0 \!important;
  line-height: 1.2 \!important;
}

.modern-feature-desc {
  font-family: 'Inter', sans-serif \!important;
  font-size: 13px \!important;
  color: var(--text-secondary) \!important;
  margin: 0 \!important;
  line-height: 1.4 \!important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .modern-chat-layout {
    flex-direction: column;
    gap: 20px;
  }
  
  .modern-chat-sidebar {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
  }
  
  .modern-stats-card,
  .modern-features-card,
  .modern-quick-actions {
    flex-shrink: 0;
    width: 280px;
  }
  
  .modern-chat-container {
    height: auto;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .modern-chat-layout {
    gap: 16px;
  }
  
  .modern-chat-sidebar {
    flex-direction: column;
  }
  
  .modern-stats-card,
  .modern-features-card,
  .modern-quick-actions {
    width: 100%;
  }
  
  .modern-chat-header {
    padding: 16px 20px;
  }
  
  .modern-chat-icon {
    width: 40px;
    height: 40px;
  }
  
  .modern-chat-title {
    font-size: 18px \!important;
  }
  
  .modern-message-content {
    max-width: 90%;
  }
  
  .modern-messages-container {
    padding: 16px;
  }
  
  .modern-chat-input-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .modern-chat-header {
    padding: 12px 16px;
  }
  
  .modern-chat-title-container {
    gap: 12px;
  }
  
  .modern-chat-icon {
    width: 36px;
    height: 36px;
  }
  
  .modern-chat-title {
    font-size: 16px \!important;
  }
  
  .modern-chat-subtitle {
    font-size: 12px \!important;
  }
  
  .modern-message-content {
    max-width: 95%;
  }
  
  .modern-messages-container {
    padding: 12px;
  }
  
  .modern-chat-input-container {
    padding: 12px;
  }
  
  .modern-input-field {
    padding: 12px 16px;
  }
  
  .modern-actions-grid {
    grid-template-columns: 1fr;
  }
}

/* 专业化特性卡片样式 */
.professional-brand-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin-top: 40px;
}

/* Glassmorphism Card Style */
.glassmorphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.professional-feature-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  backdrop-filter: blur(20px) saturate(1.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 360px;
  cursor: pointer;
}

.professional-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary) 0%, var(--accent-secondary) 50%, var(--tech-400) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.professional-feature-card:hover::before {
  opacity: 1;
}

.professional-feature-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(37, 99, 235, 0.4);
  box-shadow: 
    0 16px 40px rgba(37, 99, 235, 0.2),
    0 8px 24px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.16) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.feature-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.professional-feature-card:hover .feature-icon-wrapper {
  transform: rotate(5deg) scale(1.1);
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
}

.feature-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.3) 0%, rgba(8, 145, 178, 0.3) 100%);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
  animation: featureGlow 3s ease-in-out infinite;
}

@keyframes featureGlow {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

.professional-feature-card:hover .feature-glow {
  opacity: 0.6;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-content h4 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.feature-content p {
  font-family: 'Inter', sans-serif;
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.professional-feature-card:hover .feature-content h4 {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.professional-feature-card:hover .feature-content p {
  color: var(--text-primary);
}

/* 登录选项样式 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0;
}

.remember-checkbox {
  font-family: 'Inter', sans-serif !important;
  font-size: 14px !important;
  color: var(--text-secondary) !important;
  font-weight: 500 !important;
}

.remember-checkbox .ant-checkbox {
  border-radius: 4px !important;
  border-color: var(--border-primary) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.remember-checkbox .ant-checkbox:hover {
  border-color: var(--accent-primary) !important;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1) !important;
}

.remember-checkbox .ant-checkbox-checked {
  background: var(--accent-primary) !important;
  border-color: var(--accent-primary) !important;
}

.remember-checkbox .ant-checkbox-checked::after {
  border-color: white !important;
}

.remember-checkbox:hover {
  color: var(--text-primary) !important;
}

/* AppLayout动画 */
@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* 优化的布局样式 */
.app-layout {
  position: relative;
  min-height: 100vh;
}

.app-layout-content {
  min-height: 100vh;
  background: transparent;
  position: relative;
  z-index: 1;
}

/* 优化的Header样式 */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px) saturate(1.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 80px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.brand-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-container {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  position: relative;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
  animation: logoFloat 6s ease-in-out infinite;
}

.logo-sparkle {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #0891b2 0%, #22d3ee 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: sparkle 4s ease-in-out infinite;
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.brand-title {
  margin: 0 !important;
  line-height: 1.1;
  font-weight: 700 !important;
  font-size: 20px !important;
  background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500 !important;
  line-height: 1.2 !important;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-icon {
  color: rgba(255, 255, 255, 0.5);
  transform: rotate(-90deg);
}

.breadcrumb-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

/* 时间显示 */
.time-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.time-text {
  color: white;
  font-size: 14px;
  font-weight: 600;
  font-family: monospace;
  line-height: 1.2;
}

.date-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  line-height: 1.2;
}

/* 通知按钮 */
.notification-btn {
  width: 40px !important;
  height: 40px !important;
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease !important;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  color: white !important;
  transform: scale(1.05) !important;
  border-color: rgba(37, 99, 235, 0.3) !important;
}

/* 用户下拉 */
.user-dropdown {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

.user-avatar {
  background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
  min-width: 0;
}

.user-name {
  color: white !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  margin: 0 !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

.user-role {
  display: flex;
  align-items: center;
  gap: 4px;
}

.role-text {
  font-size: 11px !important;
  color: rgba(255, 255, 255, 0.6) !important;
  text-transform: capitalize !important;
  margin: 0 !important;
}

.dropdown-icon {
  color: rgba(255, 255, 255, 0.5);
  transition: transform 0.3s ease;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 菜单项样式 */
.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  transition: all 0.3s ease;
}

.menu-item.logout {
  color: #ef4444;
}

/* Content区域 */
.app-content {
  padding: 32px;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 80px - 60px);
}

/* Footer样式 */
.app-footer {
  text-align: center;
  padding: 16px 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  position: relative;
  z-index: 1;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.footer-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.footer-item:first-child svg {
  color: #2563eb;
}

.footer-item:nth-child(3) svg {
  color: #0891b2;
}

.footer-item:last-child svg {
  color: #22d3ee;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s ease-in-out infinite;
}

/* 优化的工具卡片样式 */
.tool-card {
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-card:hover {
  background: var(--bg-elevated);
  transform: translateY(-2px);
}

.tool-icon-container {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(37, 99, 235, 0.3);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.2);
}

.tool-card:hover .tool-icon-container {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(37, 99, 235, 0.3);
}

.featured-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  animation: pulse 2s ease-in-out infinite;
}

.tool-name {
  color: var(--text-primary) !important;
  font-weight: 500 !important;
  text-align: center !important;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.tool-card:hover .tool-name {
  color: var(--accent-primary) !important;
}

/* 简化的动画 */
@keyframes logoFloat {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-3px); 
  }
}

@keyframes sparkle {
  0%, 100% { 
    transform: rotate(0deg) scale(1); 
    opacity: 0.8; 
  }
  50% { 
    transform: rotate(180deg) scale(1.1); 
    opacity: 1; 
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1); 
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .time-display {
    display: none;
  }
  
  .footer-content {
    gap: 12px;
  }
  
  .footer-item:nth-child(3),
  .footer-item:last-child {
    display: none;
  }
}

@media (max-width: 480px) {
  .brand-info {
    display: none;
  }
  
  .user-info {
    display: none;
  }
  
  .app-content {
    padding: 16px;
  }
}
