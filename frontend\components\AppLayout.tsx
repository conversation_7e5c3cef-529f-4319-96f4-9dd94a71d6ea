import React, { useState, useEffect } from 'react';
import { Layout, Avatar, Dropdown, Space, Typography } from 'antd';
import { 
  User, 
  LogOut, 
  Settings, 
  LayoutDashboard, 
  Database, 
  Star,
  ChevronDown,
  Shield
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '@/store/auth';
import { MolecularBackground } from './MolecularBackground';
import { UserSettingsPanel } from './UserSettingsPanel';

const { Header, Content, Footer } = Layout;
const { Title, Text: AntdText } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
  pageTitle?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children, pageTitle }) => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [settingsPanelOpen, setSettingsPanelOpen] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const menuItems = [
    {
      key: 'dashboard',
      label: (
        <div className="menu-item">
          <LayoutDashboard size={16} />
          <span>控制台</span>
        </div>
      ),
      onClick: () => navigate('/dashboard'),
    },
    {
      key: 'settings',
      label: (
        <div className="menu-item">
          <Settings size={16} />
          <span>用户设置</span>
        </div>
      ),
      onClick: () => setSettingsPanelOpen(true),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: (
        <div className="menu-item logout">
          <LogOut size={16} />
          <span>退出登录</span>
        </div>
      ),
      onClick: logout,
    },
  ];

  return (
    <div className="app-layout">
      <MolecularBackground />
      
      <Layout className="app-layout-content">
        <Header className="app-header">
          {/* 左侧品牌区域 */}
          <div className="header-left">
            <div className="brand-container">
              <div className="logo-container">
                <Database size={24} />
                <div className="logo-sparkle">
                  <Star size={8} />
                </div>
              </div>
              
              <div className="brand-info">
                <Title level={4} className="brand-title">
                  IDEALAB
                </Title>
                <AntdText className="brand-subtitle">
                  专业数据实验室
                </AntdText>
              </div>
            </div>

            {/* 面包屑 */}
            {pageTitle && (
              <div className="breadcrumb">
                <ChevronDown size={14} className="breadcrumb-icon" />
                <span className="breadcrumb-text">
                  {pageTitle}
                </span>
              </div>
            )}
          </div>

          {/* 右侧用户区域 */}
          <Space align="center" size="large">
            {/* 时间显示 */}
            <div className="time-display">
              <div className="time-text">{currentTime.toLocaleTimeString()}</div>
              <div className="date-text">{currentTime.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}</div>
            </div>



            {/* 用户下拉菜单 */}
            <Dropdown 
              menu={{ items: menuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <div className="user-dropdown">
                <Avatar 
                  size={36} 
                  src={user?.avatar} 
                  className="user-avatar"
                >
                  {user?.username?.charAt(0).toUpperCase()}
                </Avatar>
                <div className="user-info">
                  <AntdText className="user-name">
                    {user?.username}
                  </AntdText>
                  <div className="user-role">
                    <Shield size={10} />
                    <AntdText className="role-text">
                      {user?.role}
                    </AntdText>
                  </div>
                </div>
                <ChevronDown size={14} className="dropdown-icon" />
              </div>
            </Dropdown>
          </Space>
        </Header>

        <Content className="app-content">
          {children}
        </Content>

        <Footer className="app-footer">
          <div className="footer-content">
            <div className="footer-item">
              <Database size={14} />
              <span>IDEALAB Professional v2.0</span>
            </div>
            <div className="footer-item">
              <div className="status-dot" />
              <span>运行状态正常</span>
            </div>
            <div className="footer-item">
              <Database size={14} />
              <span>在线工具: 8个</span>
            </div>
            <div className="footer-item">
              <Shield size={14} />
              <span>所有系统正常</span>
            </div>
          </div>
        </Footer>
      </Layout>
      
      {/* 用户设置面板 */}
      <UserSettingsPanel 
        open={settingsPanelOpen}
        onClose={() => setSettingsPanelOpen(false)}
      />
    </div>
  );
}; 