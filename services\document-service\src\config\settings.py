from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8004
    DEBUG: bool = True
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000", "http://30.c13.plus", "http://**************"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "30.c13.plus", "**************"]
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "idealab_minio_user"
    MINIO_SECRET_KEY: str = "your_minio_secret_key_here"
    MINIO_SECURE: bool = False
    MINIO_BUCKET: str = "documents"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # 文件配置
    UPLOAD_DIR: str = "./uploads"
    TEMP_DIR: str = "./temp"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [
        "pdf", "png", "jpg", "jpeg", "tiff", "bmp", 
        "xlsx", "xls", "csv", "docx", "doc"
    ]
    
    # OCR配置
    TESSERACT_CMD: str = "tesseract"
    OCR_LANGUAGES: List[str] = ["chi_sim", "eng", "chi_tra"]
    OCR_CONFIDENCE_THRESHOLD: float = 0.5
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"


settings = Settings()
