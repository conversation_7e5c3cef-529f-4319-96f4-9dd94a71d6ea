import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Typography, Statistic, Divider, Button, Space, Tooltip } from 'antd';
import { UserOutlined, BankOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { EquityNode } from '@/components/AdvancedEquityGraph';

const { Title, Text } = Typography;

interface SidebarContentProps {
    node: any | null;
    onNodeExpand?: (nodeId: string, direction: 'up' | 'down') => void;
}

const DetailItem: React.FC<{ title: string; content: React.ReactNode }> = ({ title, content }) => {
    if (!content) return null;
    return (
        <div style={{ 
            marginBottom: '8px', 
            paddingBottom: '8px',
            borderBottom: '1px solid rgba(255, 255, 255, 0.08)'
        }}>
            <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'flex-start',
                gap: '8px'
            }}>
                <Text className="!text-gray-400" style={{ 
                    fontSize: '11px', 
                    fontWeight: 500,
                    minWidth: '70px',
                    lineHeight: '1.3',
                    flexShrink: 0
                }}>
                    {title}
                </Text>
                <div style={{ flex: 1, textAlign: 'right', minWidth: 0 }}>
                    {typeof content === 'string' && content.length > 20 ? (
                        <Tooltip title={content} placement="topLeft">
                            <Text className="!text-white" style={{ 
                                fontSize: '12px', 
                                lineHeight: '1.3',
                                display: 'block',
                                wordBreak: 'break-all',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                maxWidth: '140px'
                            }}>
                                {content}
                            </Text>
                        </Tooltip>
                    ) : (
                        <Text className="!text-white" style={{ 
                            fontSize: '12px', 
                            lineHeight: '1.3',
                            wordBreak: 'break-all'
                        }}>
                            {content}
                        </Text>
                    )}
                </div>
            </div>
        </div>
    );
};

export const SidebarContent: React.FC<SidebarContentProps> = ({ node, onNodeExpand }) => (
    <AnimatePresence>
        {node && (
            <motion.div key={node.id} initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -10 }}>
                <Title level={5} style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }} className="text-white">
                    {node.type === 'person' ? <UserOutlined className="mr-2" /> : <BankOutlined className="mr-2" />}
                    <Text ellipsis={{ tooltip: node.name }} className="!text-white">{node.name}</Text>
                </Title>

                {node.percentage && <Statistic title={<Text className="!text-gray-300">穿透持股比例</Text>} value={node.percentage} suffix="%" valueStyle={{ color: '#fff', fontSize: '18px' }} />}
                
                <Divider style={{ background: 'rgba(255,255,255,0.2)' }} />
                
                {onNodeExpand && (node.expandable?.up || node.expandable?.down) && (
                    <>
                        <Title level={5} className="!text-white mb-3">节点操作</Title>
                        <Space className="w-full">
                            {node.expandable.up && (
                                <Button
                                    icon={<ArrowUpOutlined />}
                                    onClick={() => onNodeExpand(node.id, 'up')}
                                    className="w-full"
                                >
                                    向上展开
                                </Button>
                            )}
                            {node.expandable.down && (
                                <Button
                                    icon={<ArrowDownOutlined />}
                                    onClick={() => onNodeExpand(node.id, 'down')}
                                    className="w-full"
                                >
                                    向下展开
                                </Button>
                            )}
                        </Space>
                        <Divider style={{ background: 'rgba(255,255,255,0.2)' }} />
                    </>
                )}

                <DetailItem title="法定代表人" content={node.法定代表人} />
                <DetailItem title="注册资本" content={node.注册资本} />
                <DetailItem title="成立日期" content={node.成立日期} />
                <DetailItem title="天眼评分" content={node.天眼评分} />
                <DetailItem title="行业" content={node.行业} />
                <DetailItem title="组织机构代码" content={node.组织机构代码} />
                <DetailItem title="经营范围" content={node.经营范围} />
                <DetailItem title="标签" content={node.标签} />
                
            </motion.div>
        )}
    </AnimatePresence>
); 