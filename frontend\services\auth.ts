import { api, ApiResponse } from './api';

// 认证相关类型定义
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  role?: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  permissions: string[];
  avatar?: string;
}

export interface AuthResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// 认证API服务
export const authApi = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> => {
    try {
      const response = await api.post<AuthResponse>('/api/auth/login', credentials);
      
      // 保存token到localStorage
      if (response.success && response.data) {
        localStorage.setItem('auth_token', response.data.tokens.accessToken);
        localStorage.setItem('refresh_token', response.data.tokens.refreshToken);
        localStorage.setItem('user_data', JSON.stringify(response.data.user));
      }
      
      return response;
    } catch (error: any) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 用户注册
  register: async (userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> => {
    try {
      const response = await api.post<AuthResponse>('/api/auth/register', userData);
      
      // 注册成功后也保存token
      if (response.success && response.data) {
        localStorage.setItem('auth_token', response.data.tokens.accessToken);
        localStorage.setItem('refresh_token', response.data.tokens.refreshToken);
        localStorage.setItem('user_data', JSON.stringify(response.data.user));
      }
      
      return response;
    } catch (error: any) {
      console.error('注册失败:', error);
      throw error;
    }
  },

  // 刷新token
  refreshToken: async (): Promise<ApiResponse<{ tokens: { accessToken: string; refreshToken: string } }>> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('没有刷新令牌');
      }

      const response = await api.post('/api/auth/refresh', { refreshToken });
      
      if (response.success && response.data) {
        localStorage.setItem('auth_token', response.data.tokens.accessToken);
        localStorage.setItem('refresh_token', response.data.tokens.refreshToken);
      }
      
      return response;
    } catch (error: any) {
      console.error('刷新token失败:', error);
      // 清除所有认证信息
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
      throw error;
    }
  },

  // 验证token
  verifyToken: async (token: string): Promise<ApiResponse<{ user: User }>> => {
    try {
      const response = await api.post('/api/auth/verify', { token });
      return response;
    } catch (error: any) {
      console.error('验证token失败:', error);
      throw error;
    }
  },

  // 登出
  logout: async (): Promise<void> => {
    try {
      // 清除本地存储的认证信息
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
      
      // 可以调用后端登出API（如果有的话）
      // await api.post('/api/auth/logout');
    } catch (error: any) {
      console.error('登出失败:', error);
    }
  },

  // 获取当前用户信息
  getCurrentUser: (): User | null => {
    try {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');
    return !!(token && userData);
  },

  // 获取用户权限
  getUserPermissions: (): string[] => {
    const user = authApi.getCurrentUser();
    return user?.permissions || [];
  },

  // 检查用户是否有特定权限
  hasPermission: (permission: string): boolean => {
    const permissions = authApi.getUserPermissions();
    return permissions.includes(permission);
  },

  // 检查用户角色
  hasRole: (role: string): boolean => {
    const user = authApi.getCurrentUser();
    return user?.role === role;
  },

  // 获取认证token
  getAuthToken: (): string | null => {
    return localStorage.getItem('auth_token');
  }
};

export default authApi;