[tool.poetry]
name = "idealab-financial-service"
version = "1.0.0"
description = "IDEALAB Financial Data Service for Equity and Bond Analysis"
authors = ["IDEALAB Team <<EMAIL>>"]
license = "MIT"
package-mode = false

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
pydantic = "^2.5.0"
pydantic-settings = "^2.3.0"
networkx = "^3.2.1"
neo4j = "^5.15.0"
pandas = "^2.1.0"
numpy = "^1.24.0"
matplotlib = "^3.8.0"
plotly = "^5.17.0"
sqlalchemy = "^2.0.23"
psycopg2-binary = "^2.9.9"
alembic = "^1.13.0"
requests = "^2.31.0"
python-multipart = "^0.0.6"
python-dotenv = "^1.0.0"
redis = "^5.0.1"
celery = "^5.3.4"
pyvis = "^0.3.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.7.0"

[tool.poetry.scripts]
start = "run:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"