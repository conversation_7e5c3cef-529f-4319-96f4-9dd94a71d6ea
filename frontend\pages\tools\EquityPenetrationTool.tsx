import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Layout, AutoComplete, Button, Typography, message, Spin, Statistic, Divider, Empty, Tooltip, Input, Slider, Space, Badge, Tag } from 'antd';
import { ProjectOutlined, PartitionOutlined, ApartmentOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { AdvancedEquityGraph, EquityNode } from '@/components/AdvancedEquityGraph';
import { GlassmorphicCard } from '@/components/GlassmorphicCard';
import { SidebarContent } from '@/components/SidebarContent';
import { financialApi } from '@/services';
import { useDebounce } from '@/hooks/useDebounce'; // Assuming you have this hook

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

const fetchInitialEquityData = async (companyName: string) => {
    try {
        // This function now correctly returns a Promise of ApiResponse<EquityPenetrationResponse> or null
        return await financialApi.equityPenetration({
            company_id: companyName,
            up_depth: 2,
            down_depth: 2
        });
    } catch (error) {
        console.error('Failed to fetch initial equity data:', error);
        message.error('获取初始股权数据失败，请检查后端服务');
        return null;
    }
};

const searchCompanies = async (keyword: string): Promise<{ value: string }[]> => {
    if (!keyword) return [];
    try {
        const response = await financialApi.searchCompanies(keyword);
        if (response && response.suggestions) {
            return response.suggestions.map((company: any) => ({
                value: company.name
            }));
        }
        return [];
    } catch (error) {
        console.error('Failed to search companies:', error);
        return [];
    }
};

export const EquityPenetrationTool: React.FC = () => {
    const [loading, setLoading] = useState(true);
    // State now holds the flat graph data structure
    const [graphData, setGraphData] = useState<{ nodes: any[], edges: any[] }>({ nodes: [], edges: [] });
    const [selectedNode, setSelectedNode] = useState<any | null>(null);
    const [totalNodes, setTotalNodes] = useState(0);
    const [visibleNodes, setVisibleNodes] = useState(0); // 新增：可见节点数量
    const [companyName, setCompanyName] = useState("北京海开控股（集团）股份有限公司");
    const [searchValue, setSearchValue] = useState('');
    const [options, setOptions] = useState<{ value: string }[]>([]);
    const [equityThreshold, setEquityThreshold] = useState<number>(0);
    // 新增：关系过滤状态
    const [showShareholders, setShowShareholders] = useState(true); // 股东关系
    const [showInvestments, setShowInvestments] = useState(true);   // 对外投资
    // 新增：特征标签状态
    const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
    const debouncedSearchValue = useDebounce(searchValue, 300);

    const loadData = useCallback(async (name: string) => {
        if (!name.trim()) {
            message.warning('请输入公司名称');
            return;
        }
        setLoading(true);
        setSelectedNode(null);
        setGraphData({ nodes: [], edges: [] });
        
        try {
            const response = await fetchInitialEquityData(name);
            
            // The response is now the actual data object, not a wrapper.
            if (response && response.graph_data && response.graph_data.nodes.length > 0) {
                setGraphData(response.graph_data);
                setTotalNodes(response.graph_data.nodes.length);
                setVisibleNodes(response.graph_data.nodes.length); // 初始化可见节点数量
                
                // 更新搜索值为实际加载的企业名称（用于关系过滤的中心节点匹配）
                if (response.graph_data.nodes.length > 0) {
                    // 寻找最可能是中心节点的企业
                    const centerCandidate = response.graph_data.nodes.find(node => 
                        node.name === name || node.企业名称 === name
                    ) || response.graph_data.nodes[0];
                    
                    const actualCompanyName = centerCandidate.name || centerCandidate.企业名称;
                    if (actualCompanyName && actualCompanyName !== searchValue) {
                        setSearchValue(actualCompanyName);
                    }
                }
                
                message.success(`成功获取 ${name} 的初始股权结构`);
            } else {
                setGraphData({ nodes: [], edges: [] });
                setTotalNodes(0);
                setVisibleNodes(0);
                message.info(`未找到 ${name} 的相关股权信息`);
            }
        } catch (error) {
            message.error('数据加载失败');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadData(companyName);
    }, [companyName]);

    useEffect(() => {
        const fetchSuggestions = async () => {
            if (debouncedSearchValue) {
                const suggestions = await searchCompanies(debouncedSearchValue);
                setOptions(suggestions);
            } else {
                setOptions([]);
            }
        };
        fetchSuggestions();
    }, [debouncedSearchValue]);

    const handleSearch = (value: string) => {
        setSearchValue(value);
    };
    
    const handleSelect = (value: string) => {
        setCompanyName(value);
        setSearchValue(value);
    };

    // 处理可见节点数量变化的回调
    const handleVisibleNodesChange = useCallback((count: number) => {
        setVisibleNodes(count);
    }, []);

    // 计算可见节点的标签统计
    const tagStatistics = useMemo(() => {
        const tagCounts = new Map<string, number>();
        
        graphData.nodes.forEach(node => {
            const tags = node.标签 || node.properties?.标签;
            if (tags && typeof tags === 'string') {
                const tagList = tags.split('|').filter(tag => tag.trim());
                tagList.forEach(tag => {
                    const trimmedTag = tag.trim();
                    if (trimmedTag) {
                        tagCounts.set(trimmedTag, (tagCounts.get(trimmedTag) || 0) + 1);
                    }
                });
            }
        });
        
        return Array.from(tagCounts.entries())
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count); // 按数量降序
    }, [graphData.nodes]); // 只依赖原始节点数据
    
    // 处理标签点击
    const handleTagClick = useCallback((tag: string) => {
        const newSelectedTags = new Set(selectedTags);
        if (newSelectedTags.has(tag)) {
            newSelectedTags.delete(tag);
        } else {
            newSelectedTags.add(tag);
        }
        setSelectedTags(newSelectedTags);
    }, [selectedTags]);

    const handleNodeClick = useCallback((node: any) => {
        setSelectedNode(node);
    }, []);
    
    // Handler for expanding a node
    const handleNodeExpand = useCallback(async (nodeId: string, direction: 'up' | 'down') => {
        setLoading(true);
        try {
            const response = await financialApi.expandNode(nodeId, direction);
            const expansionData = response.data; // Extract data from response
            if (expansionData && expansionData.nodes) {
                // Merge new data with existing graph data
                // Use a Set to ensure nodes are unique by their ID
                setGraphData(prevData => {
                    const existingNodeIds = new Set(prevData.nodes.map(n => n.id));
                    const newNodes = expansionData.nodes.filter((n: any) => !existingNodeIds.has(n.id));

                    return {
                        nodes: [...prevData.nodes, ...newNodes],
                        edges: [...prevData.edges, ...expansionData.edges], // Note: Edge uniqueness is harder, assuming no duplicates for now
                    };
                });
                setTotalNodes(prevTotal => prevTotal + expansionData.nodes.filter((n: any) => !new Set(graphData.nodes.map(n => n.id)).has(n.id)).length);
                message.success(`节点已展开`);
            }
        } catch (error) {
            message.error('展开节点失败');
        } finally {
            setLoading(false);
        }
    }, [graphData.nodes]);

    return (
        <div style={{ width: '100%', height: 'calc(100vh - 160px)', position: 'relative' }}>
            <Layout style={{ background: 'transparent', padding: '16px', height: '100%' }}>
                <Sider width={280} style={{ background: 'transparent', marginRight: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">控制面板</Title>}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
                            <Statistic 
                                title={<Text className="!text-gray-300">节点总数</Text>} 
                                value={totalNodes} 
                                prefix={<ProjectOutlined />} 
                                valueStyle={{ color: '#fff', fontSize: '18px' }} 
                            />
                            <Statistic 
                                title={<Text className="!text-gray-300">可见节点</Text>} 
                                value={visibleNodes} 
                                prefix={<ProjectOutlined />} 
                                valueStyle={{ color: equityThreshold > 0 ? '#10b981' : '#fff', fontSize: '18px' }} 
                            />
                        </div>
                        
                        <Title level={5} className="!text-white">关系过滤</Title>
                        
                        {/* 关系类型选择 */}
                        <div style={{ marginBottom: '16px' }}>
                            <Space>
                                <Button 
                                    type={showShareholders ? "primary" : "default"}
                                    size="small"
                                    onClick={() => setShowShareholders(!showShareholders)}
                                    style={{ 
                                        backgroundColor: showShareholders ? '#1677ff' : 'transparent',
                                        borderColor: showShareholders ? '#1677ff' : 'rgba(255,255,255,0.3)'
                                    }}
                                >
                                    股东关系
                                </Button>
                                <Button 
                                    type={showInvestments ? "primary" : "default"}
                                    size="small"
                                    onClick={() => setShowInvestments(!showInvestments)}
                                    style={{ 
                                        backgroundColor: showInvestments ? '#1677ff' : 'transparent',
                                        borderColor: showInvestments ? '#1677ff' : 'rgba(255,255,255,0.3)'
                                    }}
                                >
                                    对外投资
                                </Button>
                            </Space>
                        </div>
                        
                        {/* 股权阈值过滤 */}
                        <div style={{ marginBottom: '20px' }}>
                            <Text className="!text-gray-300" style={{ fontSize: '12px' }}>显示股权比例大于 {equityThreshold}% 的关系</Text>
                            <Slider
                                min={0}
                                max={100}
                                value={equityThreshold}
                                onChange={setEquityThreshold}
                                tooltip={{ formatter: (value) => `${value}%` }}
                                style={{ marginTop: '8px' }}
                            />
                        </div>
                        
                        <Divider style={{ background: 'rgba(255,255,255,0.2)' }} />
                        
                        {/* 特征统计 */}
                        <div style={{ marginBottom: '16px', maxHeight: '200px', overflowY: 'auto', paddingTop: '8px' }}>
                            {tagStatistics.length > 0 ? (
                                <Space size={[4, 4]} wrap>
                                    {tagStatistics.map(({ tag, count }) => (
                                        <Tag
                                            key={tag}
                                            color={selectedTags.has(tag) ? 'blue' : 'default'}
                                            style={{ 
                                                cursor: 'pointer',
                                                margin: '2px',
                                                fontSize: '11px'
                                            }}
                                            onClick={() => handleTagClick(tag)}
                                        >
                                            <Badge count={count} size="small" offset={[8, -8]}>
                                                {tag}
                                            </Badge>
                                        </Tag>
                                    ))}
                                </Space>
                            ) : (
                                <Text className="!text-gray-400" style={{ fontSize: '12px' }}>
                                    暂无标签数据
                                </Text>
                            )}
                        </div>
                        
                        <Divider style={{ background: 'rgba(255,255,255,0.2)' }} />
                        <Title level={5} className="!text-white">高级分析</Title>
                        <Tooltip title="即将推出">
                           <Button icon={<PartitionOutlined />} style={{ width: '100%', marginBottom: '12px' }} disabled>社区发现</Button>
                        </Tooltip>
                         <Tooltip title="即将推出">
                           <Button icon={<ApartmentOutlined />} style={{ width: '100%' }} disabled>中心性分析</Button>
                        </Tooltip>
                    </GlassmorphicCard>
                </Sider>
                <Content style={{ position: 'relative' }}>
                    <GlassmorphicCard style={{ padding: 0 }}>
                        {loading && (
                            <div className="absolute inset-0 z-20 flex items-center justify-center bg-slate-900 bg-opacity-60 backdrop-blur-sm">
                                <Spin size="large" tip={<span className="text-white text-lg">正在查询股权穿透信息...</span>} />
                            </div>
                        )}
                        {graphData.nodes.length > 0 ? (
                            <AdvancedEquityGraph 
                                data={graphData} 
                                onNodeClick={handleNodeClick} 
                                onNodeExpand={handleNodeExpand}
                                height={window.innerHeight - 130}
                                searchProps={{
                                    value: searchValue,
                                    options: options,
                                    onSearch: handleSearch,
                                    onSelect: handleSelect,
                                    loading: loading
                                }}
                                equityThreshold={equityThreshold}
                                relationshipFilter={{
                                    showShareholders,
                                    showInvestments
                                }}
                                selectedTags={selectedTags}
                                onVisibleNodesChange={handleVisibleNodesChange}
                            />
                        ) : (
                            !loading && <Empty description="无数据显示，请输入公司名称进行查询" className="h-full flex flex-col justify-center items-center" imageStyle={{ height: 100 }} />
                        )}
                    </GlassmorphicCard>
                </Content>
                <Sider width={280} style={{ background: 'transparent', marginLeft: '16px' }}>
                    <GlassmorphicCard title={<Title level={5} className="!text-white">节点信息</Title>}>
                        {selectedNode ? <SidebarContent node={selectedNode} onNodeExpand={handleNodeExpand} /> : (
                            <div className="text-center text-gray-400 mt-10">
                                <InfoCircleOutlined className="text-3xl mb-4" />
                                <p>点击任意节点查看详细信息</p>
                            </div>
                        )}
                    </GlassmorphicCard>
                </Sider>
            </Layout>
        </div>
    );
};