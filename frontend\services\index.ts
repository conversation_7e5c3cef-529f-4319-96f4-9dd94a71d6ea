import { api, ApiResponse } from './api';

// NLP服务相关类型定义
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  history?: ChatMessage[];
  context?: string;
}

export interface ChatResponse {
  response: string;
  confidence?: number;
  sources?: string[];
}

export interface SentimentAnalysisRequest {
  texts: string[];
  language?: string;
}

export interface SentimentResult {
  text: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  emotions?: {
    joy: number;
    sadness: number;
    anger: number;
    fear: number;
    surprise: number;
  };
}

export interface SentimentAnalysisResponse {
  results: SentimentResult[];
  summary: {
    positive_count: number;
    negative_count: number;
    neutral_count: number;
    average_confidence: number;
  };
}

// 金融服务相关类型定义
export interface EquityPenetrationRequest {
  company_id: string;
  max_depth?: number;
  max_results?: number;
}

export interface EquityPenetrationResponse {
  company_name: string;
  graph_data: {
    nodes: any[];
    edges: any[];
  };
  analysis_depth: number;
  analysis_time: string;
}

export interface BondAnalysisRequest {
  bond_code: string;
  analysis_date?: string;
  analysis_type?: string[];
}

export interface BondAnalysisResponse {
  bond_code: string;
  bond_name: string;
  market_data: {
    current_price: number;
    yield_to_maturity: number;
    duration: number;
    credit_rating: string;
    trading_volume: number;
  };
  risk_metrics: {
    credit_risk: string;
    interest_rate_risk: string;
    liquidity_risk: string;
    overall_risk_score: number;
  };
  recommendations: string[];
  analysis_date: string;
}

// 文档服务相关类型定义
export interface OCRResult {
  file_name: string;
  text_content: string;
  confidence: number;
  processing_time: number;
}

export interface WatermarkRequest {
  watermark_text: string;
  position?: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  opacity?: number;
  font_size?: number;
}

export interface WatermarkResult {
  file_name: string;
  original_url: string;
  watermarked_url: string;
  processing_time: number;
}

// NLP服务API
export const nlpApi = {
  // 聊天机器人
  chat: async (request: ChatRequest): Promise<ApiResponse<ChatResponse>> => {
    return api.post('/nlp/chat', request);
  },

  // 情感分析
  sentimentAnalysis: async (request: SentimentAnalysisRequest): Promise<ApiResponse<SentimentAnalysisResponse>> => {
    return api.post('/nlp/sentiment', request);
  },
};

// 金融服务API
export const financialApi = {
  // 股权穿透分析
  equityPenetration: async (params: { company_id: string; up_depth?: number; down_depth?: number; }): Promise<ApiResponse<EquityPenetrationResponse>> => {
    return api.post('/financial/equity-penetration', null, { params });
  },

  // 节点展开
  expandNode: async (nodeId: string, direction: 'up' | 'down'): Promise<ApiResponse<any>> => {
    return api.get(`/financial/expand-node/${nodeId}`, { params: { direction } });
  },

  // 搜索公司
  searchCompanies: async (keyword: string): Promise<ApiResponse<{ suggestions: { name: string }[] }>> => {
    return api.get('/financial/search-companies', { params: { keyword } });
  },

  // 股权质押分析
  equityPledge: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.post('/financial/equity-pledge', { company_id });
  },

  // 债券分析
  bondAnalysis: async (request: BondAnalysisRequest): Promise<ApiResponse<BondAnalysisResponse>> => {
    return api.post('/financial/bond-analysis', request);
  },

  // 获取公司信息
  getCompanyInfo: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/equity/company/${company_id}/info`);
  },

  // 获取公司股东信息
  getCompanyShareholders: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/equity/company/${company_id}/shareholders`);
  },

  // 获取公司投资信息
  getCompanyInvestments: async (company_id: string): Promise<ApiResponse<any>> => {
    return api.get(`/financial/equity/company/${company_id}/investments`);
  },

  // 获取债券市场概览
  getBondMarketOverview: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/bond/market-overview');
  },

  // 获取债券投资建议
  getBondRecommendations: async (params: {
    risk_level?: string;
    investment_amount?: number;
    holding_period?: number;
  }): Promise<ApiResponse<any>> => {
    return api.get('/financial/bond/recommendations', { params });
  },
};

// 文档服务API
export const documentApi = {
  // 批量OCR
  batchOCR: async (files: File[]): Promise<ApiResponse<OCRResult[]>> => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });
    
    return api.upload('/document/ocr', formData);
  },

  // 批量加水印
  batchWatermark: async (files: File[], watermarkConfig: WatermarkRequest): Promise<ApiResponse<WatermarkResult[]>> => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });
    
    // 添加水印配置
    Object.entries(watermarkConfig).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });
    
    return api.upload('/document/watermark', formData);
  },

  // Excel文件合并
  mergeExcel: async (files: File[], options?: {
    merge_type?: 'vertical' | 'horizontal';
    include_headers?: boolean;
    output_name?: string;
  }): Promise<ApiResponse<any>> => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files`, file);
    });
    
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });
    }
    
    return api.upload('/document/excel-merge', formData);
  },
};

// 分析服务API
export const analyticsApi = {
  // 获取仪表板数据
  getDashboard: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/dashboard');
  },

  // 获取系统性能
  getPerformance: async (): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/performance');
  },

  // 获取市场趋势
  getTrends: async (days: number = 30): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/trends', { params: { days } });
  },

  // 生成报告
  generateReport: async (reportType: string = 'daily'): Promise<ApiResponse<any>> => {
    return api.get('/financial/analytics/reports', { params: { report_type: reportType } });
  },
};